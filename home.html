<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="theme-color" content="#ff5018">
    <title>MIKO - Home</title>
    <link rel="manifest" crossorigin="use-credentials" href="/manifest.json">

    <!-- Optimized Google Fonts - Single font family -->
    <link href="https://fonts.googleapis.com/css2?family=Montserrat:wght@400;500;600&display=swap" rel="stylesheet">

    <!-- Apple-specific PWA meta tags for iOS icons and full-screen mode -->
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-app-title" content="MIKO">
    <!-- Apple Touch Icon (recommended size 180x180 for modern iOS devices) -->
    <link rel="apple-touch-icon" href="/icons/icon-180x180.png">
    <link rel="stylesheet" href="/css/style.css">
</head>
<body>
    <header class="app-header">
        <div class="app-logo">MIKO</div>
        <div class="header-top-row">
            <div class="header-profile-pic">
                <img id="profilePicture" src="https://placehold.co/40x40/FF6B35/FFFFFF?text=P" alt="Profile Picture">
            </div>
            <div class="header-location" id="locationInfoClickable">
                <div class="header-location-top-row">
                    <span class="header-location-label">Delivery location</span>
                    <!-- Removed the down-arrow-icon-small-wrapper SVG as per user request -->
                </div>
                <span class="header-location-name" id="locationDetails">
                    Fetching location...
                </span>
            </div>
            <!-- Container for the right-side icons -->
            <div class="header-right-icons">
                <!-- Login Icon (for guest users) -->
                <div class="header-login-icon" id="loginIcon" onclick="goToLogin()" title="Login" style="display: none;">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="40" height="40" color="#141B34" fill="none">
                        <path d="M9 17.625C9.07356 19.4769 10.6169 21.0494 12.6844 20.9988C13.1654 20.9849 13.7599 20.8897 14.4127 20.7834C17.1279 20.3047 19.037 18.1725 19.461 15.4487C19.8783 12.7927 19.8783 11.2073 19.461 8.55129C19.037 5.82745 17.1279 3.69525 14.4127 3.21663C13.7599 3.11026 13.1654 3.01506 12.6844 3.00119C10.6169 2.95058 9.07356 4.52307 9 6.37499" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                        <path d="M14 12L4 12M4 12L7 9M4 12L7 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <!-- Logout Icon (for authenticated users) -->
                <div class="header-logout-icon" id="logoutIcon" onclick="logout()" title="Logout">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="40" height="40" color="#141B34" fill="none">
                        <path d="M15 17.625C14.9264 19.4769 13.3831 21.0494 11.3156 20.9988C10.8346 20.9849 10.2401 20.8897 9.58728 20.7834C6.87205 20.3047 4.96301 18.1725 4.53899 15.4487C4.12168 12.7927 4.12168 11.2073 4.53899 8.55129C4.96301 5.82745 6.87205 3.69525 9.58728 3.21663C10.2401 3.11026 10.8346 3.01506 11.3156 3.00119C13.3831 2.95058 14.9264 4.52307 15 6.37499" stroke="currentColor" stroke-width="1.5" stroke-linecap="round"/>
                        <path d="M10 12L20 12M20 12L17 9M20 12L17 15" stroke="currentColor" stroke-width="1.5" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </div>
                <!-- Notification Icon -->
                <div class="header-notification-icon" id="notificationIcon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="44" height="44" color="#000000" fill="none">
                        <defs />
                        <path fill="#141B34" d="M12.75,1.25 C13.233,1.25 13.776,1.333 14.221,1.624 C14.715,1.948 15,2.471 15,3.125 C15,3.589 14.859,4.069 14.629,4.484 C17.781,5.287 20.162,8.061 20.341,11.452 C20.35,11.624 20.356,11.789 20.363,11.948 C20.385,12.49 20.404,12.962 20.503,13.413 C20.62,13.953 20.837,14.377 21.278,14.708 C22.047,15.285 22.5,16.191 22.5,17.153 C22.5,18.534 21.413,19.75 19.95,19.75 L16.425,19.75 C16.077,21.462 14.564,22.75 12.75,22.75 C10.936,22.75 9.422,21.462 9.075,19.75 L5.55,19.75 C4.087,19.75 3,18.534 3,17.153 C3,16.26 3.39,15.415 4.062,14.837 L4.222,14.708 C4.663,14.377 4.88,13.953 4.997,13.413 C5.076,13.052 5.104,12.678 5.123,12.265 L5.137,11.948 C5.144,11.789 5.15,11.624 5.159,11.452 C5.338,8.061 7.719,5.287 10.871,4.484 C10.641,4.069 10.5,3.589 10.5,3.125 C10.5,2.471 10.785,1.948 11.279,1.624 C11.724,1.333 12.267,1.25 12.75,1.25 Z M12.75,5.75 C9.505,5.75 6.828,8.29 6.657,11.53 C6.651,11.658 6.645,11.793 6.64,11.934 C6.618,12.494 6.592,13.139 6.463,13.732 C6.293,14.518 5.928,15.304 5.122,15.908 C4.73,16.202 4.5,16.663 4.5,17.153 C4.5,17.768 4.976,18.25 5.55,18.25 L19.95,18.25 C20.524,18.25 21,17.768 21,17.153 C21,16.663 20.77,16.202 20.378,15.908 C19.572,15.304 19.207,14.518 19.037,13.732 C18.934,13.258 18.897,12.75 18.875,12.279 L18.86,11.934 C18.855,11.793 18.849,11.658 18.843,11.53 C18.672,8.29 15.995,5.75 12.75,5.75 Z M14.872,19.75 L10.628,19.75 C10.937,20.624 11.77,21.25 12.75,21.25 C13.73,21.25 14.563,20.624 14.872,19.75 Z M12.75,2.75 C12.405,2.75 12.198,2.815 12.1,2.879 C12.06,2.906 12.042,2.929 12.032,2.949 C12.02,2.97 12,3.021 12,3.125 C12,3.357 12.103,3.672 12.291,3.926 C12.485,4.187 12.663,4.25 12.75,4.25 C12.837,4.25 13.015,4.187 13.209,3.926 C13.397,3.672 13.5,3.357 13.5,3.125 C13.5,3.021 13.48,2.97 13.468,2.949 C13.458,2.929 13.44,2.906 13.4,2.879 C13.302,2.815 13.095,2.75 12.75,2.75 Z" />
                    </svg>
                </div>
                <!-- Small Search Icon (initially hidden) -->
                <div class="header-search-icon-small" id="smallSearchIcon">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="44" height="44" color="#ffffff" fill="none">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M1.74902 9.49219C1.74902 13.7622 5.22902 17.2422 9.49902 17.2422C11.3648 17.2422 13.0797 16.5778 14.4194 15.4734C14.4347 15.4921 14.4511 15.5102 14.4685 15.5276L15.9685 17.0276L15.9686 17.0277C15.5784 17.8566 15.725 18.8793 16.4084 19.5627L18.4484 21.6028C18.8784 22.0328 19.4484 22.2428 20.0184 22.2428V22.2528C20.5884 22.2528 21.1584 22.0328 21.5984 21.6028C22.4684 20.7328 22.4684 19.3127 21.5984 18.4427L19.5584 16.4028C18.8744 15.7267 17.8568 15.5821 17.0301 15.969L17.0285 15.9675L15.5285 14.4675C15.5113 14.4503 15.4934 14.4341 15.475 14.4189C16.5825 13.0783 17.249 11.3609 17.249 9.49219C17.249 5.22219 13.769 1.74219 9.49902 1.74219C5.22902 1.74219 1.74902 5.22219 1.74902 9.49219ZM3.24902 9.49219C3.24902 6.04219 6.04902 3.24219 9.49902 3.24219C12.949 3.24219 15.749 6.04219 15.749 9.49219C15.749 12.9422 12.949 15.7422 9.49902 15.7422C6.04902 15.7422 3.24902 12.9422 3.24902 9.49219ZM17.4584 18.4928L19.4984 20.5328C19.7784 20.8128 20.2484 20.8128 20.5384 20.5328C20.8184 20.2528 20.8184 19.7828 20.5384 19.4928L18.4984 17.4527C18.2184 17.1727 17.7484 17.1727 17.4584 17.4527C17.1784 17.7327 17.1784 18.2028 17.4584 18.4928Z" fill="#141B34" />
                    </svg>
                </div>
            </div>
        </div>
        <div class="header-bottom-row">
            <h1 class="header-greeting">What you'd like<br>to eat for today?</h1>
            <div class="header-search-bar">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="44" height="44" color="#ffffff" fill="none">
                    <path fill-rule="evenodd" clip-rule="evenodd" d="M1.74902 9.49219C1.74902 13.7622 5.22902 17.2422 9.49902 17.2422C11.3648 17.2422 13.0797 16.5778 14.4194 15.4734C14.4347 15.4921 14.4511 15.5102 14.4685 15.5276L15.9685 17.0276L15.9686 17.0277C15.5784 17.8566 15.725 18.8793 16.4084 19.5627L18.4484 21.6028C18.8784 22.0328 19.4484 22.2428 20.0184 22.2428V22.2528C20.5884 22.2528 21.1584 22.0328 21.5984 21.6028C22.4684 20.7328 22.4684 19.3127 21.5984 18.4427L19.5584 16.4028C18.8744 15.7267 17.8568 15.5821 17.0301 15.969L17.0285 15.9675L15.5285 14.4675C15.5113 14.4503 15.4934 14.4341 15.475 14.4189C16.5825 13.0783 17.249 11.3609 17.249 9.49219C17.249 5.22219 13.769 1.74219 9.49902 1.74219C5.22902 1.74219 1.74902 5.22219 1.74902 9.49219ZM3.24902 9.49219C3.24902 6.04219 6.04902 3.24219 9.49902 3.24219C12.949 3.24219 15.749 6.04219 15.749 9.49219C15.749 12.9422 12.949 15.7422 9.49902 15.7422C6.04902 15.7422 3.24902 12.9422 3.24902 9.49219ZM17.4584 18.4928L19.4984 20.5328C19.7784 20.8128 20.2484 20.8128 20.5384 20.5328C20.8184 20.2528 20.8184 19.7828 20.5384 19.4928L18.4984 17.4527C18.2184 17.1727 17.7484 17.1727 17.4584 17.4527C17.1784 17.7327 17.1784 18.2028 17.4584 18.4928Z" fill="#141B34" />
                </svg>
                <input type="text" placeholder="Search menu, restaurant or craving">
                <i class="fas fa-sliders-h header-filter-icon"></i>
                <!-- Removed the header-reload-icon as per user request -->
            </div>
        </div>
    </header>

    <main class="main-content">
        <!-- Categories Section -->
        <section class="categories-section">
            <div class="section-header-container">
                <h2 id="categoriesHeading" class="actual-content-hidden">Categories</h2>
            </div>
            <div class="categories-grid-skeleton skeleton-container">
                <!-- Skeletons will be dynamically generated here -->
            </div>
            <div id="categoriesGrid" class="categories-grid actual-content-hidden">
                <!-- Categories will be dynamically loaded here -->
            </div>
        </section>

        <!-- Food Items Section (Popular Now) -->
        <section class="food-items-section">
            <!-- Removed the heading and heading skeleton for food items -->
            <div class="food-items-grid-skeleton skeleton-container">
                <!-- Skeletons will be dynamically generated here -->
            </div>
            <div id="foodItemsGrid" class="food-items-grid actual-content-hidden">
                <!-- Food items will be dynamically loaded here -->
            </div>
        </section>

        <!-- Offers Section -->
        <section class="offers-section">
            <div class="section-header-container">
                <h2 id="offersHeading" class="actual-content-hidden">#SpecialForYou</h2>
            </div>
            <div id="offersGridSkeleton" class="offers-grid-skeleton skeleton-container">
                <!-- Skeletons will be dynamically generated here -->
            </div>
            <div id="offersGrid" class="offers-grid actual-content-hidden">
                <!-- Offers will be dynamically loaded here -->
            </div>
        </section>

        <!-- New Restaurants Section -->
        <section class="restaurants-section">
            <div class="section-header-container">
                <h2 id="restaurantsHeading" class="actual-content-hidden">Nearby Restaurants</h2>
            </div>
            <div id="restaurantsGridSkeleton" class="restaurants-grid-skeleton skeleton-container">
                <!-- Skeletons will be dynamically generated here -->
            </div>
            <div id="restaurantsGrid" class="restaurants-grid actual-content-hidden">
                <!-- Restaurants will be dynamically loaded here -->
            </div>
        </section>
    </main>

    <!-- New Floating Bottom Navigation Bar -->
    <nav class="nav">
        <ul class="nav__list">
            <li>
                <a href="/home.html" class="nav__link active-link" id="homeNavItem">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="1.6rem" height="1.6rem" fill="currentColor">
                        <defs />
                        <path fill="currentColor" d="M9.526,2.049 C10.352,1.549 11.126,1.25 12,1.25 C12.874,1.25 13.648,1.549 14.474,2.049 C15.274,2.532 16.188,3.246 17.335,4.142 L18.373,4.952 C18.422,4.99 18.471,5.028 18.518,5.065 C19.894,6.138 20.784,6.832 21.267,7.825 C21.751,8.817 21.751,9.947 21.75,11.695 L21.75,14.028 C21.75,15.872 21.75,17.333 21.597,18.476 C21.44,19.652 21.108,20.603 20.36,21.354 C19.611,22.105 18.662,22.438 17.49,22.596 C16.35,22.75 14.894,22.75 13.057,22.75 L10.943,22.75 C9.106,22.75 7.65,22.75 6.511,22.596 C5.338,22.438 4.389,22.105 3.64,21.354 C2.892,20.603 2.561,19.652 2.403,18.476 C2.25,17.333 2.25,15.872 2.25,14.028 L2.25,11.88 C2.25,11.818 2.25,11.756 2.25,11.696 L2.25,11.696 C2.249,9.947 2.249,8.817 2.733,7.825 C3.216,6.832 4.106,6.138 5.482,5.065 C5.530,5.028 5.578,4.99 5.627,4.952 L6.665,4.142 C7.813,3.246 8.727,2.532 9.526,2.049 Z M10.302,3.332 C9.588,3.764 8.744,4.422 7.550,5.354 L6.550,6.134 C4.976,7.363 4.394,7.840 4.081,8.482 C3.768,9.124 3.750,9.879 3.750,11.880 L3.750,13.972 C3.750,15.885 3.752,17.245 3.890,18.277 C4.025,19.287 4.279,19.870 4.703,20.295 C5.126,20.720 5.706,20.974 6.711,21.110 C7.739,21.248 9.093,21.250 11.000,21.250 L13.000,21.250 C14.907,21.250 16.261,21.248 17.289,21.110 C18.295,20.974 18.874,20.720 19.297,20.295 C19.721,19.870 19.975,19.287 20.110,18.277 C20.249,17.245 20.250,15.885 20.250,13.972 L20.250,11.880 C20.250,9.879 20.232,9.124 19.919,8.482 C19.606,7.840 19.024,7.363 17.450,6.134 L16.450,5.354 C15.256,4.422 14.412,3.764 13.698,3.332 C13.001,2.911 12.499,2.750 12.000,2.750 C11.501,2.750 11.000,2.911 10.302,3.332 Z" />
                    </svg>
                </a>
            </li>

            <li>
                <a href="/cart.html" class="nav__link" id="cartNavItem">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="1.6rem" height="1.6rem" fill="currentColor">
                        <defs />
                        <path fill="currentColor" d="M1.75,2 C1.75,1.586 2.086,1.25 2.5,1.25 L3.438,1.25 C4.7,1.25 5.8,2.109 6.106,3.333 L6.11,3.347 L6.614,5.75 L19.448,5.75 C19.952,5.75 20.415,5.75 20.786,5.809 C21.201,5.875 21.633,6.033 21.939,6.454 C22.233,6.859 22.271,7.318 22.242,7.737 C22.215,8.132 22.117,8.618 22.006,9.17 L21.996,9.217 C21.591,11.231 21.192,13.131 20.253,14.5 C19.767,15.21 19.134,15.785 18.293,16.176 C17.46,16.562 16.461,16.75 15.263,16.75 L8.463,16.75 C7.643,16.753 6.921,17.380 6.776,18.250 L17.5,18.250 C18.743,18.250 19.750,19.257 19.750,20.500 C19.750,21.743 18.743,22.750 17.500,22.750 C16.257,22.750 15.250,21.743 15.250,20.500 C15.250,20.237 15.295,19.985 15.378,19.750 L12.622,19.750 C12.705,19.985 12.750,20.237 12.750,20.500 C12.750,21.743 11.743,22.750 10.500,22.750 C9.257,22.750 8.250,21.743 8.250,20.500 C8.250,20.237 8.295,19.985 8.378,19.750 L6.411,19.750 C5.741,19.750 5.250,19.193 5.250,18.571 C5.250,17.243 6.015,16.071 7.138,15.545 L5.290,6.742 C5.271,6.686 5.258,6.627 5.253,6.565 L4.648,3.685 C4.505,3.135 4.008,2.750 3.438,2.750 L2.500,2.750 C2.086,2.750 1.750,2.414 1.750,2.000 Z M8.880,15.250 L15.263,15.250 C16.309,15.250 17.079,15.085 17.661,14.815 C18.236,14.548 18.667,14.162 19.016,13.653 C19.747,12.585 20.104,11.019 20.526,8.921 C20.649,8.307 20.726,7.919 20.746,7.634 C20.755,7.499 20.748,7.422 20.740,7.379 C20.734,7.349 20.728,7.340 20.726,7.337 L20.723,7.335 C20.707,7.327 20.659,7.307 20.551,7.290 C20.312,7.252 19.970,7.250 19.394,7.250 L6.929,7.250 L8.609,15.250 L8.880,15.250 Z M9.750,20.500 C9.750,20.914 10.086,21.250 10.500,21.250 C10.914,21.250 11.250,20.914 11.250,20.500 C11.250,20.086 10.914,19.750 10.500,19.750 C10.086,19.750 9.750,20.086 9.750,20.500 Z M17.500,19.750 C17.086,19.750 16.750,20.086 16.750,20.500 C16.750,20.914 17.086,21.250 17.500,21.250 C17.914,21.250 18.250,20.914 18.250,20.500 C18.250,20.086 17.914,19.750 17.500,19.750 Z" />
                    </svg>
                </a>
            </li>

            <li>
                <a href="#Qr" class="nav__qr-button" id="qrButtonNavItem">
                    <svg width="232px" height="232px" viewBox="0 0 32 32" enable-background="new 0 0 32 32" version="1.1" xml:space="preserve" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#ffffff" stroke="#ffffff"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"></g><g id="SVGRepo_iconCarrier"> <g id="Layer_1"></g> <g id="Layer_2"> <g> <polyline fill="none" points=" 30,6 30,2 26,2 " stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"></polyline> <polyline fill="none" points=" 6,2 2,2 2,6 " stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"></polyline> <polyline fill="none" points=" 2,26 2,30 6,30 " stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"></polyline> <polyline fill="none" points=" 26,30 30,30 30,26 " stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"></polyline> <rect fill="none" height="8" stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2" width="8" x="5" y="5"></rect> <rect fill="none" height="8" stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2" width="8" x="19" y="5"></rect> <rect fill="none" height="8" stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2" width="8" x="5" y="19"></rect> <polyline fill="none" points=" 19,23 19,19 23,19 " stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"></polyline> <polyline fill="none" points=" 27,23 27,27 23,27 " stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2"></polyline> <rect fill="none" height="2" stroke="#ffffff" stroke-linecap="round" stroke-linejoin="round" stroke-miterlimit="10" stroke-width="2" width="2" x="22" y="22"></rect> </g> </g> </g></svg>
                </a>
            </li>

            <li>
                <a href="/membership.html" class="nav__link" id="membershipNavItem">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="1.6rem" height="1.6rem" fill="currentColor">
                        <defs />
                        <path fill="currentColor" d="M10.453,3.25 C10.453,3.25 13.548,3.25 13.548,3.25 C15.387,3.25 16.831,3.25 17.969,3.379 C19.132,3.51 20.074,3.784 20.857,4.402 C21.092,4.587 21.309,4.792 21.507,5.014 C22.173,5.761 22.471,6.667 22.612,7.778 C22.750,8.857 22.750,10.223 22.750,11.947 L22.750,12.053 C22.750,13.777 22.750,15.143 22.612,16.222 C22.471,17.333 22.173,18.239 21.507,18.986 C21.309,19.208 21.092,19.413 20.857,19.598 C20.074,20.216 19.132,20.490 17.969,20.621 C16.831,20.750 15.387,20.750 13.547,20.750 L10.453,20.750 C8.613,20.750 7.169,20.750 6.031,20.621 C4.868,20.490 3.926,20.216 3.143,19.598 C2.908,19.413 2.691,19.208 2.493,18.986 C1.827,18.239 1.529,17.333 1.388,16.222 C1.250,15.143 1.250,13.777 1.250,12.053 L1.250,11.947 C1.250,10.223 1.250,8.857 1.388,7.778 C1.529,6.667 1.827,5.761 2.493,5.014 C2.691,4.792 2.908,4.587 3.143,4.402 C3.926,3.784 4.868,3.510 6.031,3.379 C7.169,3.250 8.613,3.250 10.452,3.250 L10.453,3.250 Z M2.765,9.750 C2.750,10.396 2.750,11.138 2.750,12.000 C2.750,13.789 2.751,15.059 2.876,16.032 C2.997,16.985 3.226,17.554 3.613,17.988 C3.752,18.144 3.905,18.289 4.072,18.420 C4.544,18.793 5.168,19.014 6.199,19.131 C7.244,19.249 8.603,19.250 10.500,19.250 L13.500,19.250 C15.397,19.250 16.757,19.249 17.801,19.131 C18.832,19.014 19.456,18.793 19.928,18.420 C20.095,18.289 20.248,18.144 20.387,17.988 C20.774,17.554 21.003,16.985 21.124,16.032 C21.249,15.059 21.250,13.789 21.250,12.000 C21.250,11.138 21.250,10.396 21.235,9.750 Z M4.072,5.579 C3.905,5.711 3.752,5.856 3.613,6.012 C3.226,6.446 2.997,7.015 2.876,7.968 C2.864,8.059 2.853,8.153 2.844,8.250 L21.156,8.250 C21.147,8.153 21.136,8.059 21.124,7.968 C21.003,7.015 20.774,6.446 20.387,6.012 C20.248,5.856 20.095,5.711 19.928,5.579 C19.456,5.207 18.832,4.986 17.801,4.869 C16.757,4.751 15.397,4.750 13.500,4.750 L10.500,4.750 C8.603,4.750 7.244,4.751 6.199,4.869 C5.168,4.986 4.544,5.207 4.072,5.579 Z M13.750,16.000 C13.750,15.586 14.086,15.250 14.500,15.250 L18.000,15.250 C18.414,15.250 18.750,15.586 18.750,16.000 C18.750,16.414 18.414,16.750 18.000,16.750 L14.500,16.750 C14.086,16.750 13.750,16.414 13.750,16.000 Z M10.000,15.250 L11.500,15.250 C11.914,15.250 12.250,15.586 12.250,16.000 C12.250,16.414 11.914,16.750 11.500,16.750 L10.000,16.750 C9.586,16.750 9.250,16.414 9.250,16.000 C9.250,15.586 9.586,15.250 10.000,15.250 Z" />
                    </svg>
                </a>
            </li>

            <li>
                <a href="/profile.html" class="nav__link" id="profileNavItem">
                    <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="1.6rem" height="1.6rem" fill="currentColor">
                        <path fill-rule="evenodd" clip-rule="evenodd" d="M12 14.25C8.55 14.25 5.75 17.05 5.75 20.5C5.75 20.91 5.41 21.25 5 21.25C4.59 21.25 4.25 20.91 4.25 20.5C4.25 17.3105 6.19168 14.5617 8.95394 13.3749C7.33129 12.3571 6.25 10.5522 6.25 8.5C6.25 5.33 8.83 2.75 12 2.75C15.17 2.75 17.75 5.33 17.75 8.5C17.75 10.5522 16.6687 12.3571 15.0461 13.3749C17.8083 14.5617 19.75 17.3105 19.75 20.5C19.75 20.91 19.41 21.25 19 21.25C18.59 21.25 18.25 20.91 18.25 20.5C18.25 17.05 15.45 14.25 12 14.25ZM12 12.75C9.66 12.75 7.75 10.84 7.75 8.5C7.75 6.16 9.66 4.25 12 4.25C14.34 4.25 16.25 6.16 16.25 8.5C16.25 10.84 14.34 12.75 12 12.75Z" fill="currentColor" />
                    </svg>
                </a>
            </li>
        </ul>
    </nav>

    <!-- RESTAURANT CARD TEMPLATE (NO HTML IN JS) -->
    <template id="restaurant-card-template">
        <a href="#restaurant-" class="restaurant-card">
            <div class="restaurant-image-container">
                <img class="restaurant-image" src="" alt="" loading="lazy">
                <div class="restaurant-favorite-wrapper">
                    <svg class="heart-icon-outline" viewBox="0 0 24 24" fill="none">
                        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" stroke="white" stroke-width="2" fill="none"/>
                    </svg>
                    <svg class="heart-icon-filled hidden" viewBox="0 0 24 24" fill="none">
                        <path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z" fill="#FF6B35"/>
                    </svg>
                </div>
            </div>
            <div class="restaurant-info">
                <div class="restaurant-header">
                    <h3 class="restaurant-name"></h3>
                    <div class="restaurant-rating">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="#e1b105">
                            <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
                        </svg>
                        <span class="rating-value"></span> <span class="review-count"></span>
                    </div>
                </div>
                <div class="restaurant-time-cuisine-row">
                    <div class="restaurant-delivery-time">
                        <svg fill="#949494" width="16px" height="16px" viewBox="0 0 24 24">
                            <path d="M12,0A12,12,0,1,0,24,12,12.013,12.013,0,0,0,12,0Zm0,22A10,10,0,1,1,22,12,10.011,10.011,0,0,1,12,22Zm2-10a2,2,0,1,1-3-1.723V7a1,1,0,0,1,2,0v3.277A1.994,1.994,0,0,1,14,12Z"/>
                        </svg>
                        Est. <span class="delivery-time-text"></span>
                    </div>
                    <span class="dot-separator">&bull;</span>
                    <div class="restaurant-cuisine-type"></div>
                </div>
                <div class="restaurant-distance-delivery-row">
                    <div class="restaurant-distance-info">
                        <svg class="distance-icon-svg" viewBox="-3 0 20 20" fill="currentColor">
                            <path d="M374,5297 C372.178,5297 369,5290.01 369,5286 C369,5283.243 371.243,5281 374,5281 C376.757,5281 379,5283.243 379,5286 C379,5290.01 375.822,5297 374,5297"/>
                        </svg>
                        <span class="distance-text">-•-</span>
                    </div>
                    <span class="dash-separator">•</span>
                    <div class="restaurant-delivery-time">
                        <svg class="time-icon-svg" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm.5 11H11v6l5.25 3.15.75-1.23-4.5-2.67V7z"/>
                        </svg>
                        <span class="time-text">-•-</span>
                    </div>
                    <span class="dash-separator">•</span>
                    <div class="restaurant-delivery-charges">
                        <svg class="delivery-icon-svg" width="16px" height="16px" viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 5.25C12.4142 5.25 12.75 5.58579 12.75 6V6.31673C14.3804 6.60867 15.75 7.83361 15.75 9.5C15.75 9.91421 15.4142 10.25 15 10.25C14.5858 10.25 14.25 9.91421 14.25 9.5C14.25 8.65573 13.3765 7.75 12 7.75C10.6235 7.75 9.75 8.65573 9.75 9.5"/>
                        </svg>
                        <span class="delivery-text">-•-</span>
                    </div>
                </div>
                <div class="restaurant-info-separator"></div>
                <div class="restaurant-offers-container"></div>
            </div>
        </a>
    </template>

    <script>
        document.addEventListener('DOMContentLoaded', async () => {
            const profilePicture = document.getElementById('profilePicture');
            const homeNavItem = document.getElementById('homeNavItem');
            const qrButtonNavItem = document.getElementById('qrButtonNavItem');
            const membershipNavItem = document.getElementById('membershipNavItem');
            const cartNavItem = document.getElementById('cartNavItem');
            const categoriesGrid = document.getElementById('categoriesGrid'); // Get the categories grid container
            const foodItemsGrid = document.getElementById('foodItemsGrid'); // Get the food items grid container
            const offersGrid = document.getElementById('offersGrid'); // Get the offers grid container
            const restaurantsGrid = document.getElementById('restaurantsGrid'); // Get the new restaurants grid container


            const appHeader = document.querySelector('.app-header'); // Get the app header element
            const headerGreeting = document.querySelector('.header-greeting');
            const headerSearchBar = document.querySelector('.header-search-bar');
            const notificationIcon = document.getElementById('notificationIcon');
            const smallSearchIcon = document.getElementById('smallSearchIcon');
            const headerBottomRow = document.querySelector('.header-bottom-row'); // Get the header-bottom-row element
            const locationDetails = document.getElementById('locationDetails');

            // PRIORITY 1: LOCATION VARIABLES (MOST IMPORTANT)
            let userLatitude = null;
            let userLongitude = null;
            const DEFAULT_LATITUDE = 35.6895;
            const DEFAULT_LONGITUDE = 139.6917;

            // Essential globals
            let currencySymbol = '$';
            let globalRestaurantsData = [];
            let allOffers = []; // Global variable to store all offers

            // Function to get basic location name from coordinates
            const getLocationName = (lat, lon) => {
                // Basic location identification based on coordinate ranges
                const latitude = parseFloat(lat);
                const longitude = parseFloat(lon);

                // China coordinate ranges
                if (latitude >= 18 && latitude <= 54 && longitude >= 73 && longitude <= 135) {
                    // More specific regions within China
                    if (latitude >= 32 && latitude <= 37 && longitude >= 110 && longitude <= 118) {
                        // Henan Province area (your location)
                        if (latitude >= 33.5 && latitude <= 34.5 && longitude >= 113 && longitude <= 114.5) {
                            return "Central China";
                        }
                        return "Henan Province, China";
                    } else if (latitude >= 39.5 && latitude <= 41 && longitude >= 115.5 && longitude <= 117.5) {
                        return "Beijing, China";
                    } else if (latitude >= 31 && latitude <= 31.5 && longitude >= 121 && longitude <= 122) {
                        return "Shanghai, China";
                    } else if (latitude >= 22.5 && latitude <= 23.5 && longitude >= 113.5 && longitude <= 114.5) {
                        return "Guangzhou, China";
                    } else if (latitude >= 22 && latitude <= 22.8 && longitude >= 113.8 && longitude <= 114.5) {
                        return "Shenzhen, China";
                    }
                    return "China";
                }

                // Other major regions
                if (latitude >= 25 && latitude <= 49 && longitude >= -125 && longitude <= -66) {
                    return "United States";
                } else if (latitude >= 41 && latitude <= 83 && longitude >= -141 && longitude <= -52) {
                    return "Canada";
                } else if (latitude >= 49.5 && latitude <= 61 && longitude >= -8 && longitude <= 2) {
                    return "United Kingdom";
                } else if (latitude >= 35.5 && latitude <= 71 && longitude >= -5.5 && longitude <= 10) {
                    return "Europe";
                }

                // Fallback with coordinates
                return `Location: ${latitude.toFixed(4)}, ${longitude.toFixed(4)}`;
            };

            // Function to get user's current location
            const getUserLocation = () => {
                return new Promise((resolve, reject) => {
                    // Set initial "Fetching Location" text
                    locationDetails.textContent = "Fetching Location...";

                    if (navigator.geolocation) {
                        navigator.geolocation.getCurrentPosition(
                            async (position) => {
                                userLatitude = position.coords.latitude; // Store globally
                                userLongitude = position.coords.longitude; // Store globally
                                // Simple location display without external API dependency
                                const locationName = getLocationName(userLatitude, userLongitude);
                                locationDetails.textContent = locationName;

                                // Refresh restaurant cards with actual location data
                                fetchAndDisplayRestaurants();

                                // Try geocoding as enhancement, but don't depend on it
                                try {
                                    const response = await fetch(`geocode.php?lat=${userLatitude}&lon=${userLongitude}`);
                                    const data = await response.json();

                                    if (data.success && data.address) {
                                        locationDetails.textContent = data.address;

                                        // Update restaurant distances again after geocoding
                                        updateRestaurantDistances(userLatitude, userLongitude, globalRestaurantsData);

                                        resolve({
                                            lat: userLatitude,
                                            lon: userLongitude,
                                            address: data.address,
                                            postal_code: data.postal_code,
                                            full_address: data.full_address
                                        });
                                        return;
                                    }
                                } catch (error) {
                                    // Geocoding failed, keep basic location
                                }

                                // Always resolve with coordinates and basic location
                                resolve({
                                    lat: userLatitude,
                                    lon: userLongitude,
                                    address: locationName
                                });
                            },
                            (error) => {
                                console.error("Error getting location:", error);
                                switch (error.code) {
                                    case error.PERMISSION_DENIED:
                                        locationDetails.textContent = "Location access denied.";
                                        // Set a default location if permission is denied
                                        userLatitude = DEFAULT_LATITUDE;
                                        userLongitude = DEFAULT_LONGITUDE;
                                        break;
                                    case error.POSITION_UNAVAILABLE:
                                        locationDetails.textContent = "Location information unavailable.";
                                        // Also set default if location is unavailable
                                        userLatitude = DEFAULT_LATITUDE;
                                        userLongitude = DEFAULT_LONGITUDE;
                                        break;
                                    case error.TIMEOUT:
                                        locationDetails.textContent = "Location request timed out.";
                                        // Also set default if location times out
                                        userLatitude = DEFAULT_LATITUDE;
                                        userLongitude = DEFAULT_LONGITUDE;
                                        break;
                                    default:
                                        locationDetails.textContent = "Location error.";
                                        // Also set default for any other error
                                        userLatitude = DEFAULT_LATITUDE;
                                        userLongitude = DEFAULT_LONGITUDE;
                                        break;
                                }

                                // Refresh restaurant cards with default location
                                fetchAndDisplayRestaurants();

                                resolve({ lat: userLatitude, lon: userLongitude }); // Always resolve, providing default if needed
                            }
                        );
                    } else {
                        locationDetails.textContent = "Geolocation not supported.";
                        userLatitude = DEFAULT_LATITUDE; // Set default if geolocation is not supported
                        userLongitude = DEFAULT_LONGITUDE;

                        // Refresh restaurant cards with default location
                        fetchAndDisplayRestaurants();

                        resolve({ lat: userLatitude, lon: userLongitude }); // Resolve with default
                    }
                });
            };

            // IMMEDIATE LOCATION REQUEST
            getUserLocation();


            // Get skeleton containers
            const categoriesGridSkeleton = document.querySelector('.categories-grid-skeleton');
            const foodItemsGridSkeleton = document.querySelector('.food-items-grid-skeleton');
            const offersGridSkeleton = document.getElementById('offersGridSkeleton'); // Corrected ID selector
            const restaurantsGridSkeleton = document.getElementById('restaurantsGridSkeleton'); // Get the new restaurants skeleton grid


            // Get heading elements
            const categoriesHeading = document.getElementById('categoriesHeading');
            const offersHeading = document.getElementById('offersHeading');
            const restaurantsHeading = document.getElementById('restaurantsHeading'); // Get the new restaurants heading


            const allNavItems = [homeNavItem, cartNavItem, qrButtonNavItem, membershipNavItem, profileNavItem];

            // Placeholder for showMessageBox function (now empty as per user request)
            const showMessageBox = (message, options) => {
                // console.log("Message Box:", message, options); // Keep for debugging if needed
                // alert(message); // Removed alert as per user request
            };

            // Variable to store the name of the currently active category
            // Always default to 'Popular' as per user request
            let currentActiveCategoryName = 'Popular';

            // Function to update the profile picture based on login status and fetched avatar
            const updateProfilePicture = async () => {
                const profileImgPromise = new Promise(async (resolve) => {
                    // Check authentication using the new secure cookie system
                    const isAuthenticated = await checkSession();

                    if (isAuthenticated && currentUser) {
                        try {
                            // Use avatar from currentUser if available, otherwise use default
                            const profileImgSrc = (currentUser.avatar && currentUser.avatar !== '/avatar/default.webp')
                                ? currentUser.avatar
                                : `https://placehold.co/40x40/FF6B35/FFFFFF?text=${(currentUser.name || currentUser.email || 'U').charAt(0).toUpperCase()}`;

                            profilePicture.src = profileImgSrc;
                            profilePicture.alt = `${currentUser.name || currentUser.email}'s Profile Picture`;
                            profilePicture.onload = resolve;
                            profilePicture.onerror = resolve; // Resolve even on error
                        } catch (error) {
                            console.error("Error setting user avatar:", error);
                            profilePicture.src = "https://placehold.co/40x40/FF6B35/FFFFFF?text=U";
                            profilePicture.alt = "User Profile Picture";
                            resolve(); // Resolve even on error
                        }
                    } else {
                        // Guest user - use guest avatar
                        profilePicture.src = "https://placehold.co/40x40/9CA3AF/FFFFFF?text=G";
                        profilePicture.alt = "Guest User";
                        profilePicture.onload = resolve;
                        profilePicture.onerror = () => {
                            profilePicture.src = "https://placehold.co/40x40/9CA3AF/FFFFFF?text=G";
                            resolve(); // Resolve even if fallback is used
                        };
                    }
                });
                return profileImgPromise;
            };

            // Enhanced cache system with preloaded data support
            const apiCache = new Map();
            const CACHE_DURATION = 5 * 60 * 1000; // 5 minutes
            let preloadedCache = null;
            let isUsingPreloadedData = false;

            // Check for preloaded data from secure cookies
            const initializeCache = async () => {
                try {
                    const response = await fetch('/home.php?action=getCacheData');
                    const result = await response.json();

                    if (result.success && result.data) {
                        preloadedCache = result.data;
                        isUsingPreloadedData = true;
                        return true;
                    } else {
                        return false;
                    }
                } catch (error) {
                    return false;
                }
            };

            // Enhanced cached fetch that checks preloaded data first
            const cachedFetch = async (url) => {
                // Check preloaded cache first
                if (preloadedCache && isUsingPreloadedData) {
                    const urlParams = new URLSearchParams(url.split('?')[1]);
                    const action = urlParams.get('action');
                    const category = urlParams.get('category');

                    let cacheKey = null;
                    let responseData = null;

                    if (action === 'getCurrency') {
                        cacheKey = 'currency';
                        responseData = preloadedCache.data.currency ?
                            { success: true, currency: preloadedCache.data.currency } :
                            preloadedCache.data[cacheKey];
                    } else if (action === 'getCategories') {
                        cacheKey = 'categories';
                        responseData = preloadedCache.data[cacheKey];
                    } else if (action === 'getFoodItems') {
                        if (category === 'Popular') {
                            cacheKey = 'popularFoodItems';
                            responseData = preloadedCache.data[cacheKey];
                        } else if (category && preloadedCache.data.foodItemsByCategory) {
                            cacheKey = `foodItemsByCategory.${category}`;
                            responseData = preloadedCache.data.foodItemsByCategory[category] || [];
                        } else {
                            // Return all food items if no specific category
                            cacheKey = 'foodItems';
                            responseData = preloadedCache.data[cacheKey];
                        }
                    } else if (action === 'getOffers') {
                        cacheKey = 'offers';
                        responseData = preloadedCache.data[cacheKey];
                    } else if (action === 'getRestaurants') {
                        cacheKey = 'restaurants';
                        responseData = preloadedCache.data[cacheKey];
                    }

                    if (responseData) {
                        const itemCount = Array.isArray(responseData) ? responseData.length : (typeof responseData === 'object' ? Object.keys(responseData).length : 1);
                        // Create a mock response object
                        return {
                            ok: true,
                            json: async () => responseData
                        };
                    }
                }

                // Fall back to regular caching
                const now = Date.now();
                const cached = apiCache.get(url);

                if (cached && (now - cached.timestamp) < CACHE_DURATION) {
                    // Return a fresh response object with the cached data
                    return {
                        ok: true,
                        json: async () => cached.data
                    };
                }

                const response = await fetch(url);
                if (response.ok) {
                    // Store the parsed data instead of the response object
                    const data = await response.clone().json();
                    apiCache.set(url, {
                        data: data,
                        timestamp: now
                    });

                    // Return a fresh response object with the data
                    return {
                        ok: true,
                        json: async () => data
                    };
                }
                return response;
            };

            // Initialize cache system (async)
            let hasPreloadedData = false;
            initializeCache().then(result => {
                hasPreloadedData = result;
            });

            // Session management (consolidated)
            let currentUser = null;
            let isAuthenticated = false;

            // Check and refresh session
            const checkSession = async () => {
                try {
                    const response = await fetch('/login.php?path=/api/check-session');
                    const result = await response.json();

                    if (result.success && result.authenticated) {
                        currentUser = result.user;
                        isAuthenticated = true;
                        return true;
                    } else {
                        currentUser = null;
                        isAuthenticated = false;
                        return false;
                    }
                } catch (error) {
                    console.error('🚨 Session check failed:', error);
                    currentUser = null;
                    isAuthenticated = false;
                    return false;
                }
            };

            // Get user info
            const getUserInfo = async () => {
                try {
                    const response = await fetch('/login.php?path=/api/get-user-info');
                    const result = await response.json();

                    if (result.success && result.authenticated) {
                        return result.user;
                    }
                    return null;
                } catch (error) {
                    console.error('🚨 Failed to get user info:', error);
                    return null;
                }
            };

            // Logout function
            const logout = async () => {
                try {
                    console.log('🔄 Logging out...');
                    const response = await fetch('/login.php?path=/api/logout');
                    const result = await response.json();

                    if (result.success) {
                        currentUser = null;
                        isAuthenticated = false;
                        console.log('✅ Logged out successfully');

                        // Clear any cached data
                        await fetch('/home.php?action=clearCache').catch(() => {});

                        // Clear skip authentication as well
                        await fetch('/login.php?path=/api/clear-skip', { method: 'POST' }).catch(() => {});

                        // Redirect to login page
                        window.location.replace('/login.html');
                    } else {
                        console.error('❌ Logout failed:', result.message);
                    }
                    return result.success;
                } catch (error) {
                    console.error('🚨 Logout failed:', error);
                    // Even if logout API fails, redirect to login for security
                    window.location.replace('/login.html');
                    return false;
                }
            };

            // Check if authentication was skipped
            const checkSkipStatus = async () => {
                try {
                    const response = await fetch('/login.php?path=/api/check-skip-status');
                    const result = await response.json();

                    if (result.success && result.skipped) {
                        console.log('⏭️ Authentication was skipped, allowing access');
                        return true;
                    }
                    return false;
                } catch (error) {
                    console.error('🚨 Skip status check failed:', error);
                    return false;
                }
            };

            // Global utility functions (moved outside initializeApp for global access)

            // Function aliases for backward compatibility
            const loadCategories = () => fetchCategoriesAndDisplay();
            const loadFoodItems = (category) => fetchAndDisplayFoodItems(category, foodItemsGrid);
            const loadOffers = () => fetchAndDisplayOffers(offersGrid, offersGridSkeleton, offersHeading);
            const loadRestaurants = () => fetchAndDisplayRestaurants();

            // Content loading state
            let hasLoadedContent = false;

            // Function to calculate estimated delivery time
            const estimatedDeliveryTime = (distance) => {
                if (distance <= 2) return '15-25 min';
                if (distance <= 5) return '25-35 min';
                if (distance <= 10) return '35-45 min';
                return '45-60 min';
            };

            // Function to calculate delivery charges based on distance
            const calculateDeliveryCharges = (distance) => {
                if (distance <= 2) return 'Free';
                if (distance <= 5) return `${currencySymbol}2`;
                if (distance <= 10) return `${currencySymbol}5`;
                return `${currencySymbol}8`;
            };

            // SIMPLIFIED: Create restaurant card from template (NO HTML IN JS)
            const createRestaurantCard = (restaurant, distance, deliveryTime, deliveryCharges) => {
                const template = document.getElementById('restaurant-card-template');
                const card = template.content.cloneNode(true);
                const link = card.querySelector('.restaurant-card');

                // Set basic info
                link.href = `#restaurant-${restaurant.id}`;
                card.querySelector('.restaurant-image').src = restaurant.image_url || 'https://placehold.co/300x200/FF6B35/FFFFFF?text=Restaurant';
                card.querySelector('.restaurant-image').alt = restaurant.name;
                card.querySelector('.restaurant-name').textContent = restaurant.name;
                card.querySelector('.rating-value').textContent = restaurant.rating || '4.5';
                card.querySelector('.review-count').textContent = `(${restaurant.review_count || '100'})`;
                card.querySelector('.delivery-time-text').textContent = deliveryTime;
                card.querySelector('.restaurant-cuisine-type').textContent = restaurant.cuisine_type || 'Various';

                // Set distance info
                card.querySelector('.distance-text').textContent = distance ? `${distance.toFixed(1)} km` : '-•-';
                card.querySelector('.time-text').textContent = deliveryTime;
                card.querySelector('.delivery-text').textContent = deliveryCharges;

                return card;
            };



            // Initialize session on page load (no redirection, trust index.html routing)
            const initializeApp = async () => {
                const isAuthenticated = await checkSession();
                if (isAuthenticated) {
                    initializeAppContent();
                    return;
                }

                const isSkipped = await checkSkipStatus();
                if (isSkipped) {
                    initializeAppContent();
                    return;
                }

                initializeAppContent();
            };



            // Function to initialize app content after authentication
            const initializeAppContent = () => {
                if (isAuthenticated && currentUser) {
                    updateHeaderForAuthenticatedUser();
                } else {
                    updateHeaderForGuestUser();
                }

                // Load initial data
                loadCategories();
                loadFoodItems('Popular');
                loadOffers();
                loadRestaurants();
                getUserLocation();

                // Mark content as loaded
                hasLoadedContent = true;
            };

            // Update header for authenticated user
            const updateHeaderForAuthenticatedUser = () => {
                const profilePic = document.getElementById('profilePicture');
                if (profilePic && currentUser) {
                    // Set user avatar or default
                    if (currentUser.avatar && currentUser.avatar !== '/avatar/default.webp') {
                        profilePic.src = currentUser.avatar;
                    } else {
                        // Create initials avatar
                        const initials = (currentUser.name || currentUser.email || 'U').charAt(0).toUpperCase();
                        profilePic.src = `https://placehold.co/40x40/FF6B35/FFFFFF?text=${initials}`;
                    }
                    profilePic.title = `Logged in as ${currentUser.email}`;
                }

                // Show logout button and hide login button for authenticated users
                const loginIcon = document.getElementById('loginIcon');
                const logoutIcon = document.getElementById('logoutIcon');

                if (loginIcon) {
                    loginIcon.style.display = 'none';
                }
                if (logoutIcon) {
                    logoutIcon.style.display = 'flex';
                }
            };

            // Update header for guest user
            const updateHeaderForGuestUser = () => {
                const profilePic = document.getElementById('profilePicture');
                if (profilePic) {
                    profilePic.src = 'https://placehold.co/40x40/9CA3AF/FFFFFF?text=G';
                    profilePic.title = 'Guest mode (authentication skipped)';
                }

                // Show login button and hide logout button for guest users
                const loginIcon = document.getElementById('loginIcon');
                const logoutIcon = document.getElementById('logoutIcon');

                if (loginIcon) {
                    loginIcon.style.display = 'flex';
                }
                if (logoutIcon) {
                    logoutIcon.style.display = 'none';
                }
            };

            // Go to login page
            const goToLogin = () => {
                window.location.href = '/login.html';
            };

            // Start the app
            initializeApp();

            // Function to fetch currency symbol
            const fetchCurrencySymbol = async () => {
                try {
                    const response = await cachedFetch('/home.php?action=getCurrency');
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    const data = await response.json();
                    if (data.success && data.currency) {
                        currencySymbol = data.currency;
                    }
                } catch (error) {
                    // Use default currency symbol on error
                }
            };

            // Function to format price with fetched currency symbol
            const formatPrice = (price) => {
                // Use toLocaleString with 'en-PK' locale for Pakistani Rupee formatting
                // This handles large numbers by adding commas as thousands separators
                // and now allows for 2 decimal places.
                return `${currencySymbol} ${price.toLocaleString('en-PK', { minimumFractionDigits: 2, maximumFractionDigits: 2 })}`;
            };

            // --- Distance Calculation Function (Haversine formula) ---
            const calculateDistance = (lat1, lon1, lat2, lon2) => {
                const R = 6371; // Radius of Earth in kilometers
                const dLat = (lat2 - lat1) * Math.PI / 180;
                const dLon = (lon2 - lon1) * Math.PI / 180;
                const a =
                    Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
                    Math.sin(dLon / 2) * Math.sin(dLon / 2);
                const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
                const distance = R * c; // Distance in kilometers
                return distance; // Return distance as number (formatting done in template)
            };

            // --- Estimated Delivery Time Calculation ---
            const calculateEstimatedDeliveryTime = (distanceKm) => {
                // Simple linear model: base time + time per km
                const BASE_TIME_MINUTES = 10; // Minimum delivery time
                const MINUTES_PER_KM = 3; // Minutes per kilometer
                const estimatedTime = BASE_TIME_MINUTES + (distanceKm * MINUTES_PER_KM);
                // Add some randomness for more realistic feel (e.g., +/- 5 minutes)
                const randomVariation = Math.floor(Math.random() * 11) - 5; // -5 to +5 minutes
                return Math.max(10, Math.round(estimatedTime + randomVariation)); // Ensure minimum 10 minutes
            };

            // --- Update Restaurant Distances, Times, and Delivery Charges ---
            const updateRestaurantDistances = (userLat, userLon, restaurants) => {
                restaurants.forEach(restaurant => {
                    const restaurantLat = parseFloat(restaurant.latitude);
                    const restaurantLon = parseFloat(restaurant.longitude);

                    // Calculate distance
                    let distance = parseFloat(calculateDistance(userLat, userLon, restaurantLat, restaurantLon));
                    let displayDistance;

                    // Convert to meters if distance is less than 1 km
                    if (distance < 1) {
                        displayDistance = `${Math.round(distance * 1000)} m away`;
                    } else {
                        displayDistance = `${distance.toFixed(1)} km away`;
                    }

                    // Calculate estimated delivery time
                    const estimatedTime = calculateEstimatedDeliveryTime(distance);
                    const displayTime = `${estimatedTime} min`;

                    // Calculate delivery charges
                    const deliveryFeePerKm = parseFloat(restaurant.delivery_fee) || 0;
                    let totalDeliveryPrice = 0;
                    let deliveryPriceText = '';

                    if (deliveryFeePerKm === 0) {
                        deliveryPriceText = 'Free delivery';
                    } else {
                        totalDeliveryPrice = deliveryFeePerKm * distance;
                        deliveryPriceText = `${currencySymbol} ${totalDeliveryPrice.toFixed(0)} delivery`;
                    }

                    // Update the DOM elements
                    const distanceElement = document.getElementById(`distance-${restaurant.id}`);
                    const timeElement = document.getElementById(`time-${restaurant.id}`);
                    const deliveryElement = document.getElementById(`delivery-${restaurant.id}`);

                    if (distanceElement) {
                        distanceElement.textContent = displayDistance;
                    }
                    if (timeElement) {
                        timeElement.textContent = displayTime;
                    }
                    if (deliveryElement) {
                        deliveryElement.textContent = deliveryPriceText;
                    }
                });
            };

            // --- Skeleton Generation Functions ---

            /**
             * Generates a DocumentFragment containing category pill skeletons.
             * @param {number} count The number of skeletons to generate.
             * @returns {DocumentFragment} A DocumentFragment with the generated skeletons.
             */
            const generateCategorySkeletons = (count) => {
                const fragment = document.createDocumentFragment();
                for (let i = 0; i < count; i++) {
                    const skeletonPill = document.createElement('div');
                    skeletonPill.classList.add('skeleton-pill', 'skeleton-placeholder');
                    skeletonPill.innerHTML = `
                        <div class="skeleton-circle"></div>
                        <div class="skeleton-text short"></div>
                    `;
                    fragment.appendChild(skeletonPill);
                }
                return fragment;
            };

            /**
             * Generates a DocumentFragment containing food item card skeletons.
             * @param {number} count The number of skeletons to generate.
             * @returns {DocumentFragment} A DocumentFragment with the generated skeletons.
             */
            const generateFoodCardSkeletons = (count) => {
                const fragment = document.createDocumentFragment();
                for (let i = 0; i < count; i++) {
                    const skeletonCard = document.createElement('div');
                    skeletonCard.classList.add('skeleton-food-card', 'skeleton-placeholder');
                    skeletonCard.innerHTML = `
                        <div class="skeleton-card-image"></div>
                        <div class="skeleton-card-text long"></div>
                        <div class="skeleton-card-text medium"></div>
                        <div class="skeleton-text short"></div>
                    `;
                    fragment.appendChild(skeletonCard);
                }
                return fragment;
            };

            /**
             * Generates a DocumentFragment containing offer banner skeletons.
             * @param {number} count The number of skeletons to generate.
             * @returns {DocumentFragment} A DocumentFragment with the generated skeletons.
             */
            const generateOfferBannerSkeletons = (count) => {
                const fragment = document.createDocumentFragment();
                for (let i = 0; i < count; i++) {
                    const skeletonBanner = document.createElement('div');
                    skeletonBanner.classList.add('skeleton-offer-banner', 'skeleton-placeholder');
                    fragment.appendChild(skeletonBanner);
                }
                return fragment;
            };

            /**
             * Generates a DocumentFragment containing restaurant card skeletons.
             * @param {number} count The number of skeletons to generate.
             * @returns {DocumentFragment} A DocumentFragment with the generated skeletons.
             */
            const generateRestaurantCardSkeletons = (count) => {
                const fragment = document.createDocumentFragment();
                for (let i = 0; i < count; i++) {
                    const skeletonCard = document.createElement('div');
                    skeletonCard.classList.add('skeleton-restaurant-card', 'skeleton-placeholder');
                    skeletonCard.innerHTML = `
                        <div class="skeleton-card-image"></div>
                        <div class="skeleton-card-text-container">
                            <div class="skeleton-card-text long"></div>
                            <div class="skeleton-card-text medium"></div>
                            <div class="skeleton-text short"></div>
                        </div>
                    `;
                    fragment.appendChild(skeletonCard);
                }
                return fragment;
            };

            // --- End Skeleton Generation Functions ---


            // Function to fetch and display food items based on category
            const fetchAndDisplayFoodItems = async (category, targetGrid) => {
                const foodItemImagePromises = []; // Local array for food item images
                try {
                    // Update the section title only for the "Popular Now" section
                    // Removed the logic to update section title as headings are removed.

                    let foodItems = [];
                    const response = await cachedFetch(`/home.php?action=getFoodItems&category=${encodeURIComponent(category)}`);
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    foodItems = await response.json();

                    targetGrid.innerHTML = ''; // Clear existing food items

                    if (foodItems.length === 0) {
                        targetGrid.innerHTML = '<p style="text-align: center; width: 100%; color: var(--subtle-detail);">No food items found for this category.</p>';
                        return foodItemImagePromises; // Return empty array if no items
                    }

                    // Create a DocumentFragment to minimize DOM manipulations
                    const fragment = document.createDocumentFragment();

                    foodItems.forEach(item => {
                        const foodItemCard = document.createElement('a');
                        foodItemCard.href = `#food-item-${item.id}`; // Example link
                        foodItemCard.classList.add('food-item-card');

                        const imageUrl = item.image && item.image !== 'null' ? item.image : `https://placehold.co/150x120/FF6B35/FFFFFF?text=${encodeURIComponent(item.item_name)}&font=Montserrat`;

                        let priceContent = `<div class="food-item-price">${formatPrice(item.price)}</div>`;
                        if (item.original_price && item.original_price > item.price) {
                            priceContent = `
                                <span class="food-item-original-price">${formatPrice(item.original_price)}</span>
                                <div class="food-item-price">${formatPrice(item.price)}</div>
                            `;
                        }

                        // Generate random reviews and distance for demo purposes
                        // Use actual average_rating and review_count from restaurant data (if available on food items)
                        // For food items, if no rating is provided, use a default or random for demo
                        const reviews = (Math.random() * (5.0 - 3.5) + 3.5).toFixed(1);
                        const distance = (Math.random() * (5.0 - 0.5) + 0.5).toFixed(1);

                        // Calculate discount percentage
                        let discountTag = '';
                        if (item.original_price && item.original_price > item.price) {
                            const discountAmount = item.original_price - item.price;
                            const discountPercentage = Math.round((discountAmount / item.original_price) * 100);
                            discountTag = `
                                <div class="discount-tag">
                                    <svg viewBox="0 0 24 24" fill="#ffffff" xmlns="http://www.w3.org/2000/svg" stroke="#000000" stroke-width="0.00024000000000000003">
                                        <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                        <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#0d0c0c" stroke-width="2.112"></g>
                                        <g id="SVGRepo_iconCarrier">
                                            <path d="M21.5289 10.8689L20.0089 9.34891C19.7489 9.08891 19.5389 8.57891 19.5389 8.21891V6.05891C19.5389 5.17891 18.8189 4.45891 17.9389 4.45891H15.7889C15.4289 4.45891 14.9189 4.24891 14.6589 3.98891L13.1389 2.46891C12.5189 1.84891 11.4989 1.84891 10.8789 2.46891L9.33891 3.98891C9.08891 4.24891 8.57891 4.45891 8.20891 4.45891H6.05891C5.17891 4.45891 4.45891 5.17891 4.45891 6.05891V8.20891C4.45891 8.56891 4.24891 9.07891 3.98891 9.33891L2.46891 10.8589C1.84891 11.4789 1.84891 12.4989 2.46891 13.1189L3.98891 14.6389C4.24891 14.8989 4.45891 15.4089 4.45891 15.7689V17.9189C4.45891 18.7989 5.17891 19.5189 6.05891 19.5189H8.20891C8.56891 19.5189 9.07891 19.7289 9.33891 19.9889L10.8589 21.5089C11.4789 22.1289 12.4989 22.1289 13.1189 21.5089L14.6389 19.9889C14.8989 19.7289 15.4089 19.5189 15.7689 19.5189H17.9189C18.7989 18.7989 19.5189 19.5189 19.5189 17.9189V15.7689C19.5189 15.4089 19.7289 14.8989 19.9889 14.6389L21.5089 13.1189C22.1589 12.5089 22.1589 11.4889 21.5289 10.8689ZM7.99891 8.99891C7.99891 8.44891 8.44891 7.99891 8.99891 7.99891C9.54891 7.99891 9.99891 8.44891 9.99891 8.99891C9.99891 9.54891 9.55891 9.99891 8.99891 9.99891C8.44891 9.99891 7.99891 9.54891 7.99891 8.99891ZM9.52891 15.5289C9.37891 15.6789 9.18891 15.7489 8.99891 15.7489C8.80891 15.7489 8.61891 15.6789 8.46891 15.5289C8.17891 15.2389 8.17891 14.7589 8.46891 14.4689L14.4689 8.46891C14.7589 8.17891 15.2389 8.17891 15.5289 8.46891C15.8189 8.75891 15.8189 9.23891 15.5289 9.52891L9.52891 15.5289ZM14.9989 15.9989C14.4389 15.9989 13.9889 15.5489 13.9889 14.9989C13.9889 14.4489 14.4389 13.9989 14.9889 13.9989C15.5389 13.9989 15.9889 14.4489 15.9889 14.9989C15.9889 15.5489 15.5489 15.9989 14.9989 15.9989Z" fill="#ffffff"></path>
                                        </g>
                                    </svg>
                                    ${discountPercentage}% OFF
                                </div>
                            `;
                        }

                        // Build the card structure with all elements in their respective divs
                        foodItemCard.innerHTML = `
                            ${discountTag}
                            <div class="food-item-content">
                                <img data-src="${imageUrl}" alt="${item.item_name}" class="food-item-image lazy-load" src="data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='150' height='120'%3E%3Crect width='100%25' height='100%25' fill='%23f0f0f0'/%3E%3C/svg%3E" onerror="this.onerror=null;this.src='https://placehold.co/150x120/FF6B35/FFFFFF?text=${encodeURIComponent(item.item_name)}&font=Montserrat';">
                                <div class="food-item-details">
                                    <div class="food-item-name-wrapper">
                                        <div class="food-item-name">${item.item_name}</div>
                                    </div>
                                    <div class="food-item-reviews-wrapper">
                                        <svg class="star-icon-svg" viewBox="-2.1 -2.1 25.20 25.20" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" fill="#f45c1a" stroke="#f45c1a">
                                            <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                            <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#ff0000" stroke-width="0.546"></g>
                                            <g id="SVGRepo_iconCarrier">
                                                <title>star_favorite [#e1b105]</title>
                                                <desc>Created with Sketch.</desc>
                                                <defs> </defs>
                                                <g id="Page-1" stroke-width="0.273" fill="none" fill-rule="evenodd">
                                                    <g id="Dribbble-Light-Preview" transform="translate(-99.000000, -320.000000)" fill="#e1b105">
                                                        <g id="icons" transform="translate(56.000000, 160.000000)">
                                                            <path d="M60.556381,172.206 C60.1080307,172.639 59.9043306,173.263 60.0093306,173.875 L60.6865811,177.791 C60.8976313,179.01 59.9211306,180 58.8133798,180 C58.5214796,180 58.2201294,179.931 57.9282291,179.779 L54.3844766,177.93 C54.1072764,177.786 53.8038262,177.714 53.499326,177.714 C53.1958758,177.714 52.8924256,177.786 52.6152254,177.93 L49.0714729,179.779 C48.7795727,179.931 48.4782224,180 48.1863222,180 C47.0785715,180 46.1020708,179.01 46.3131209,177.791 L46.9903714,173.875 C47.0953715,173.263 46.8916713,172.639 46.443321,172.206 L43.575769,169.433 C42.4480682,168.342 43.0707186,166.441 44.6289197,166.216 L48.5916225,165.645 C49.211123,165.556 49.7466233,165.17 50.0227735,164.613 L51.7951748,161.051 C52.143775,160.35 52.8220755,160 53.499326,160 C54.1776265,160 54.855927,160.35 55.2045272,161.051 L56.9769285,164.613 C57.2530787,165.17 57.7885791,165.556 58.4080795,165.645 L62.3707823,166.216 C63.9289834,166.441 64.5516338,168.342 63.423933,169.433 L60.556381,172.206 Z" id="star_favorite-[#e1b105]"> </path>
                                                        </g>
                                                    </g>
                                                </g>
                                            </svg>
                                        ${reviews} reviews &bull; ${distance} km
                                    </div>
                                    <div class="food-item-price-row">
                                        <div class="food-item-price-section">
                                            ${priceContent}
                                        </div>
                                        <div class="food-item-favorite-wrapper">
                                            <!-- Default (unliked) SVG icon -->
                                            <svg class="heart-icon-outline" viewBox="0 0 24.00 24.00" fill="none" xmlns="http://www.w3.org/2000/svg" stroke="#808080">
                                                <path d="M15.7 4C18.87 4 21 6.98 21 9.76C21 15.39 12.16 20 12 20C11.84 20 3 15.39 3 9.76C3 6.98 5.13 4 8.3 4C10.12 4 11.31 4.91 12 5.71C12.69 4.91 13.88 4 15.7 4Z" stroke="#808080" stroke-width="1.656" stroke-linecap="round" stroke-linejoin="round"></path>
                                            </svg>
                                            <!-- Liked (filled) SVG icon - updated -->
                                            <svg class="heart-icon-filled" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="display: none;">
                                                <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                                <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#ffffff" stroke-width="0.048"></g>
                                                <g id="SVGRepo_iconCarrier">
                                                    <path d="M2 9.1371C2 14 6.01943 16.5914 8.96173 18.9109C10 19.7294 11 20.5 12 20.5C13 20.5 14 19.7294 15.0383 18.9109C17.9806 16.5914 22 14 22 9.1371C22 4.27416 16.4998 0.825464 12 5.50063C7.50016 0.825464 2 4.27416 2 9.1371Z" fill="#ff0000"></path>
                                                </g>
                                            </svg>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        `;

                        // Find the image element within the newly created card for its load promise
                        const imgElement = foodItemCard.querySelector('.food-item-image');
                        const imgLoadPromise = new Promise((resolve) => {
                            if (imgElement.complete) { // Check if image is already loaded (e.g., from cache)
                                resolve();
                            } else {
                                imgElement.onload = resolve;
                                imgElement.onerror = resolve; // Resolve even on error to not block overall loading
                            }
                        });
                        foodItemImagePromises.push(imgLoadPromise); // Push to the local array

                        fragment.appendChild(foodItemCard); // Append to fragment instead of directly to targetGrid

                        // Add event listener for the heart icon
                        const heartWrapper = foodItemCard.querySelector('.food-item-favorite-wrapper');
                        const outlineIcon = heartWrapper.querySelector('.heart-icon-outline');
                        const filledIcon = heartWrapper.querySelector('.heart-icon-filled');

                        heartWrapper.addEventListener('click', (e) => {
                            e.preventDefault(); // Prevent navigating to food item link
                            e.stopPropagation(); // Prevent card click event from firing

                            if (heartWrapper.classList.contains('liked')) {
                                heartWrapper.classList.remove('liked');
                                outlineIcon.style.display = 'block'; // Show outline
                                filledIcon.style.display = 'none'; // Hide filled
                            } else {
                                heartWrapper.classList.add('liked');
                                outlineIcon.style.display = 'none'; // Hide outline
                                filledIcon.style.display = 'block'; // Show filled
                            }
                        });
                    });
                    targetGrid.appendChild(fragment); // Append the fragment to the DOM once
                    return foodItemImagePromises; // Return the promises for these specific images
                } catch (error) {
                    console.error("Error fetching food items:", error);
                    targetGrid.innerHTML = '<p style="text-align: center; width: 100%; color: var(--subtle-detail);">No food items found for this category.</p>';
                    return foodItemImagePromises; // Return even on error
                }
            };

            // Function to fetch and display categories
            const fetchCategoriesAndDisplay = async () => {
                try {
                    const response = await cachedFetch('/home.php?action=getCategories'); // Fetch from home.php
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    let categories = await response.json();

                    // Sort categories by ranking initially
                    categories.sort((a, b) => a.ranking - b.ranking);

                    // Reorder categories to bring the currentActiveCategoryName to the front
                    let activeCategoryData = categories.find(c => c.name === currentActiveCategoryName);
                    if (activeCategoryData) {
                        categories = categories.filter(c => c.name !== currentActiveCategoryName);
                        categories.unshift(activeCategoryData); // Add active category to the beginning
                    } else if (categories.length > 0) {
                        // Fallback if currentActiveCategoryName is not found (e.g., first load and 'Popular' isn't there)
                        currentActiveCategoryName = categories[0].name; // Set the first category as active
                    }

                    categoriesGrid.innerHTML = ''; // Clear existing content

                    // Create a DocumentFragment for categories
                    const fragment = document.createDocumentFragment();

                    categories.forEach((category) => {
                        const categoryItem = document.createElement('a');
                        categoryItem.href = `#category-${category.name.toLowerCase().replace(/\s/g, '-')}`; // Example link
                        categoryItem.classList.add('category-item');
                        categoryItem.setAttribute('data-category-name', category.name); // Add data attribute for easy lookup

                        // Set active class if it's the current active category
                        if (category.name === currentActiveCategoryName) {
                            categoryItem.classList.add('active-category');
                        }

                        categoryItem.innerHTML = `
                            <span class="emoji">${category.emoji}</span>
                            <span class="name">${category.name}</span>
                        `;
                        fragment.appendChild(categoryItem); // Append to fragment

                        // Add click listener to handle active state and reordering
                        categoryItem.addEventListener('click', async (event) => {
                            event.preventDefault();
                            const clickedCategoryName = event.currentTarget.getAttribute('data-category-name');

                            // Only proceed if a different category is clicked
                            if (clickedCategoryName !== currentActiveCategoryName) {
                                currentActiveCategoryName = clickedCategoryName; // Update global active category

                                // Get all current items to prepare for FLIP animation
                                const currentItems = Array.from(categoriesGrid.children);
                                const firstRect = currentItems[0] ? currentItems[0].getBoundingClientRect() : null;

                                // Remove active class from all
                                document.querySelectorAll('.category-item').forEach(item => {
                                    item.classList.remove('active-category');
                                });

                                // Add active class to the clicked item
                                event.currentTarget.classList.add('active-category');

                                // Find the clicked item element in the DOM
                                const clickedItemElement = event.currentTarget;

                                // Perform the DOM reordering (move clicked item to first position)
                                categoriesGrid.prepend(clickedItemElement);

                                // If there was a first item before, animate the clicked item
                                if (firstRect) {
                                    const newFirstRect = clickedItemElement.getBoundingClientRect();
                                    const deltaX = firstRect.left - newFirstRect.left;

                                    // Apply the inverse transform instantly
                                    clickedItemElement.style.transform = `translateX(${deltaX}px)`;

                                    // Force a reflow to ensure the initial transform is applied
                                    void clickedItemElement.offsetWidth;

                                    // Animate to its final position
                                    clickedItemElement.style.transform = 'translateX(0)';

                                    // Scroll the grid to ensure the new first item is visible
                                    categoriesGrid.scrollTo({
                                        left: 0,
                                        behavior: 'smooth'
                                    });
                                }

                                // --- Instant update for food items grid ---
                                foodItemsGrid.classList.remove('actual-content-visible'); // Ensure it's not visible
                                foodItemsGrid.classList.add('actual-content-hidden'); // Ensure opacity 0
                                foodItemsGridSkeleton.style.display = 'flex'; // Show skeleton immediately

                                // Fetch and display food items for the new category immediately
                                const newFoodItemImagePromises = await fetchAndDisplayFoodItems(currentActiveCategoryName, foodItemsGrid);

                                // Immediately hide skeleton and show content after DOM is populated
                                foodItemsGridSkeleton.style.display = 'none'; // Hide skeleton
                                foodItemsGrid.classList.remove('actual-content-hidden'); // Remove hidden class
                                foodItemsGrid.classList.add('actual-content-visible'); // Add visible class (opacity 1)

                                // Images will load in the background, as before.
                                Promise.all(newFoodItemImagePromises).then(() => {
                                    // All images loaded. This block can be used for other post-image-load effects if desired.
                                }).catch(error => {
                                    console.error("Error loading images for new category:", error);
                                    // Handle image loading errors, but UI is already visible.
                                });
                            }
                        });
                    });
                    categoriesGrid.appendChild(fragment); // Append the fragment to the DOM once
                } catch (error) {
                    console.error("Error fetching categories:", error);
                    categoriesGrid.innerHTML = '<p>Failed to load categories. Please try again later.</p>';
                }
            };

            // Function to fetch and display offers
            const fetchAndDisplayOffers = async (targetGrid, targetSkeleton, targetHeading) => { // Removed targetHeadingSkeleton
                const offerImagePromises = []; // Local array for offer images
                try {
                    const response = await cachedFetch('/home.php?action=getOffers'); // Assuming a new endpoint for offers
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    allOffers = await response.json(); // Store all offers globally

                    // Clear existing offers for the single grid
                    targetGrid.innerHTML = '';

                    if (allOffers.length === 0) {
                        targetGrid.innerHTML = '<p style="text-align: center; width: 100%; color: var(--subtle-detail);">No offers available at the moment.</p>';
                        return offerImagePromises; // Return empty array if no items
                    }

                    // Create a DocumentFragment for offers
                    const fragment = document.createDocumentFragment();
                    allOffers.forEach(offer => {
                        // Only display offers that have a banner and link for the main offers section
                        if (offer.banner && offer.banner !== 'null' && offer.link) {
                            const offerBanner = document.createElement('a');
                            offerBanner.href = offer.link; // Use the link from the offer data
                            offerBanner.target = "_blank"; // Open in new tab
                            offerBanner.classList.add('offer-banner');

                            const bannerImageUrl = offer.banner;

                            // Create an image element and a promise for its load
                            const imgElement = new Image();
                            imgElement.src = bannerImageUrl;
                            imgElement.alt = "Offer Banner";
                            imgElement.onerror = function() {
                                this.src = `https://placehold.co/300x150/FF6B35/FFFFFF?text=Offer&font=Montserrat`;
                            };

                            const imgLoadPromise = new Promise((resolve) => {
                                imgElement.onload = resolve;
                                imgElement.onerror = resolve; // Resolve even on error
                            });
                            offerImagePromises.push(imgLoadPromise); // Push to the local array

                            let discountText = '';
                            if (offer.flat) {
                                discountText = `${currencySymbol} ${parseFloat(offer.flat).toFixed(2)} OFF`;
                            } else if (offer.discount) {
                                discountText = `${offer.discount}% OFF`;
                            }

                            offerBanner.innerHTML = `
                                <div class="overlay">
                                    <div class="discount-text">${discountText}</div>
                                    <div class="code-text">Code: ${offer.code}</div>
                                </div>
                            `;
                            offerBanner.prepend(imgElement); // Prepend the image element
                            fragment.appendChild(offerBanner); // Append to fragment
                        }
                    });
                    targetGrid.appendChild(fragment); // Append the fragment to the DOM once
                    return offerImagePromises; // Return the promises for these specific images
                } catch (error) {
                    console.error("Error fetching offers:", error);
                    targetGrid.innerHTML = '<p style="text-align: center; width: 100%; color: var(--subtle-detail);">No offers available at the moment.</p>';
                    return offerImagePromises; // Return even on error
                }
            };

            // Function to fetch and display restaurants
            const fetchAndDisplayRestaurants = async () => {
                const restaurantImagePromises = [];
                try {
                    // Show skeleton and hide actual content before fetching
                    restaurantsGridSkeleton.classList.remove('hidden');
                    restaurantsGrid.classList.add('actual-content-hidden');
                    restaurantsHeading.classList.add('actual-content-hidden');

                    // userLatitude and userLongitude are guaranteed to be set by getUserLocation
                    // either to actual coordinates or to DEFAULT_LATITUDE/DEFAULT_LONGITUDE.
                    // No need for an explicit check here.

                    const response = await cachedFetch('/home.php?action=getRestaurants');
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    const restaurants = await response.json();

                    // Store restaurants globally for distance updates
                    globalRestaurantsData = restaurants;

                    restaurantsGrid.innerHTML = ''; // Clear existing content

                    if (restaurants.length === 0) {
                        restaurantsGrid.innerHTML = '<p style="text-align: center; width: 100%; color: var(--subtle-detail);">No restaurants found.</p>';
                        // Hide skeleton and show message immediately if no restaurants
                        restaurantsGridSkeleton.classList.add('hidden');
                        restaurantsGrid.classList.remove('actual-content-hidden');
                        restaurantsGrid.classList.add('actual-content-visible');
                        restaurantsHeading.classList.remove('actual-content-hidden');
                        restaurantsHeading.classList.add('actual-content-visible');
                        return restaurantImagePromises;
                    }

                    const fragment = document.createDocumentFragment();

                    restaurants.forEach(restaurant => {
                        // Check if restaurant has valid coordinates
                        if (restaurant.latitude && restaurant.longitude) {
                            // Only calculate distance if user location is available
                            let distance, deliveryTime, deliveryCharges;

                            if (userLatitude && userLongitude) {
                                try {
                                    // Calculate distance from user location to restaurant
                                    distance = calculateDistance(
                                        userLatitude,
                                        userLongitude,
                                        parseFloat(restaurant.latitude),
                                        parseFloat(restaurant.longitude)
                                    );

                                    // Ensure distance is a valid number
                                    if (distance && !isNaN(distance) && distance > 0) {
                                        // Calculate estimated delivery time based on distance
                                        deliveryTime = estimatedDeliveryTime(distance);

                                        // Calculate delivery charges based on distance
                                        deliveryCharges = calculateDeliveryCharges(distance);
                                    } else {
                                        // Invalid distance calculation, use placeholders
                                        distance = null;
                                        deliveryTime = '-•-';
                                        deliveryCharges = '-•-';
                                    }
                                } catch (error) {
                                    console.error('Error calculating distance:', error);
                                    distance = null;
                                    deliveryTime = '-•-';
                                    deliveryCharges = '-•-';
                                }
                            } else {
                                // User location not available yet, show placeholders
                                distance = null;
                                deliveryTime = '-•-';
                                deliveryCharges = '-•-';
                            }

                            // Use actual average_rating and review_count from restaurant data
                            const rating = restaurant.average_rating ? parseFloat(restaurant.average_rating).toFixed(1) : 'N/A';
                            const reviewCount = restaurant.review_count ? parseInt(restaurant.review_count) : 0;

                            // Find ALL matching offers for the current restaurant, or "all" offers
                            // Safety check: ensure allOffers is available and is an array
                            const matchingOffers = (allOffers && Array.isArray(allOffers)) ? allOffers.filter(offer =>
                                (offer.restaurant_name && offer.restaurant_name.toLowerCase() === restaurant.name.toLowerCase()) ||
                                (offer.restaurant_name && offer.restaurant_name.toLowerCase() === 'all')
                            ) : [];

                            let couponTagsHtml = ''; // Initialize as empty string
                            if (matchingOffers.length > 0) {
                                matchingOffers.forEach(offer => {
                                    let couponText = '';
                                    if (offer.flat) {
                                        couponText = `${currencySymbol}${parseFloat(offer.flat).toFixed(0)}`;
                                    } else if (offer.discount) {
                                        couponText = `${offer.discount}%`;
                                    }
                                    if (couponText && offer.code) {
                                        couponTagsHtml += `
                                            <div class="restaurant-coupon-tag">
                                                <svg viewBox="0 0 24 24" fill="#ff0000" xmlns="http://www.w3.org/2000/svg" stroke="#ff0000" stroke-width="0.00024000000000000003">
                                                    <g id="SVGRepo_bgCarrier" stroke-width="0"></g>
                                                    <g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round" stroke="#0d0c0c" stroke-width="2.112"></g>
                                                    <g id="SVGRepo_iconCarrier">
                                                        <path d="M21.5289 10.8689L20.0089 9.34891C19.7489 9.08891 19.5389 8.57891 19.5389 8.21891V6.05891C19.5389 5.17891 18.8189 4.45891 17.9389 4.45891H15.7889C15.4289 4.45891 14.9189 4.24891 14.6589 3.98891L13.1389 2.46891C12.5189 1.84891 11.4989 1.84891 10.8789 2.46891L9.33891 3.98891C9.08891 4.24891 8.57891 4.45891 8.20891 4.45891H6.05891C5.17891 4.45891 4.45891 5.17891 4.45891 6.05891V8.20891C4.45891 8.56891 4.24891 9.07891 3.98891 9.33891L2.46891 10.8589C1.84891 11.4789 1.84891 12.4989 2.46891 13.1189L3.98891 14.6389C4.24891 14.8989 4.45891 15.4089 4.45891 15.7689V17.9189C4.45891 18.7989 5.17891 19.5189 6.05891 19.5189H8.20891C8.56891 19.5189 9.07891 19.7289 9.33891 19.9889L10.8589 21.5089C11.4789 22.1289 12.4989 22.1289 13.1189 21.5089L14.6389 19.9889C14.8989 19.7289 15.4089 19.5189 15.7689 19.5189H17.9189C18.7989 18.7989 19.5189 19.5189 19.5189 17.9189V15.7689C19.5189 15.4089 19.7289 14.8989 19.9889 14.6389L21.5089 13.1189C22.1589 12.5089 22.1589 11.4889 21.5289 10.8689ZM7.99891 8.99891C7.99891 8.44891 8.44891 7.99891 8.99891 7.99891C9.54891 7.99891 9.99891 8.44891 9.99891 8.99891C9.99891 9.54891 9.55891 9.99891 8.99891 9.99891C8.44891 9.99891 7.99891 9.54891 7.99891 8.99891ZM9.52891 15.5289C9.37891 15.6789 9.18891 15.7489 8.99891 15.7489C8.80891 15.7489 8.61891 15.6789 8.46891 15.5289C8.17891 15.2389 8.17891 14.7589 8.46891 14.4689L14.4689 8.46891C14.7589 8.17891 15.2389 8.17891 15.5289 8.46891C15.8189 8.75891 15.8189 9.23891 15.5289 9.52891L9.52891 15.5289ZM14.9989 15.9989C14.4389 15.9989 13.9889 15.5489 13.9889 14.9989C13.9889 14.4489 14.4389 13.9989 14.9889 13.9989C15.5389 13.9989 15.9889 14.4489 15.9889 14.9989C15.9889 15.5489 15.5489 15.9989 14.9989 15.9989Z" fill="#ff0000"></path>
                                                    </g>
                                                </svg>
                                                ${couponText}: ${offer.code}
                                            </div>
                                        `;
                                    }
                                });
                            }


                            // SIMPLIFIED: Use template instead of massive HTML block
                            const restaurantCardElement = createRestaurantCard(restaurant, distance, deliveryTime, deliveryCharges);
                            const restaurantCard = restaurantCardElement.querySelector('.restaurant-card');

                            const imgElement = restaurantCard.querySelector('.restaurant-image');
                            const imgLoadPromise = new Promise((resolve) => {
                                if (imgElement.complete) {
                                    resolve();
                                } else {
                                    imgElement.onload = resolve;
                                    imgElement.onerror = resolve;
                                }
                            });
                            restaurantImagePromises.push(imgLoadPromise);
                            fragment.appendChild(restaurantCard);

                            // Add event listener for the heart icon on restaurant cards
                            const heartWrapper = restaurantCard.querySelector('.restaurant-favorite-wrapper');
                            const outlineIcon = heartWrapper.querySelector('.heart-icon-outline');
                            const filledIcon = heartWrapper.querySelector('.heart-icon-filled');

                            heartWrapper.addEventListener('click', (e) => {
                                e.preventDefault(); // Prevent navigating to food item link
                                e.stopPropagation(); // Prevent card click event from firing

                                if (heartWrapper.classList.contains('liked')) {
                                    heartWrapper.classList.remove('liked');
                                    outlineIcon.style.display = 'block'; // Show outline
                                    filledIcon.style.display = 'none'; // Hide filled
                                } else {
                                    heartWrapper.classList.add('liked');
                                    outlineIcon.style.display = 'none'; // Hide outline
                                    filledIcon.style.display = 'block'; // Show filled
                                }
                            });
                        }
                    });
                    restaurantsGrid.appendChild(fragment);

                    // Wait for all restaurant images to load before hiding skeleton and showing content
                    Promise.all(restaurantImagePromises).then(() => {
                        restaurantsGridSkeleton.classList.add('hidden');
                        restaurantsGrid.classList.remove('actual-content-hidden');
                        restaurantsGrid.classList.add('actual-content-visible');
                        restaurantsHeading.classList.remove('actual-content-hidden');
                        restaurantsHeading.classList.add('actual-content-visible');
                    }).catch(error => {
                        console.error("Error loading restaurant images:", error);
                        // Still hide skeleton and show content even if some images fail
                        restaurantsGridSkeleton.classList.add('hidden');
                        restaurantsGrid.classList.remove('actual-content-hidden');
                        restaurantsGrid.classList.add('actual-content-visible');
                        restaurantsHeading.classList.remove('actual-content-hidden');
                        restaurantsHeading.classList.add('actual-content-visible');
                    });

                    return restaurantImagePromises; // Return even on error
                } catch (error) {
                    console.error("Error fetching restaurants:", error);
                    restaurantsGrid.innerHTML = '<p style="text-align: center; width: 100%; color: var(--subtle-detail);">Failed to load restaurants. Please try again later.</p>';
                    // Ensure skeleton is hidden and content is visible even on fetch error
                    restaurantsGridSkeleton.classList.add('hidden');
                    restaurantsGrid.classList.remove('actual-content-hidden');
                    restaurantsGrid.classList.add('actual-content-visible');
                    restaurantsHeading.classList.remove('actual-content-hidden');
                    restaurantsHeading.classList.add('actual-content-visible');
                    return restaurantImagePromises; // Return even on error
                }
            };

            // Function to hide all skeletons
            const hideAllSkeletons = () => {
                categoriesGridSkeleton.classList.add('hidden');
                foodItemsGridSkeleton.classList.add('hidden');
                offersGridSkeleton.classList.add('hidden');
                restaurantsGridSkeleton.classList.add('hidden'); // Hide restaurants skeleton
            };

            // Function to show all actual content
            const showAllContent = () => {
                categoriesGrid.classList.remove('actual-content-hidden');
                categoriesGrid.classList.add('actual-content-visible');

                foodItemsGrid.classList.remove('actual-content-hidden');
                foodItemsGrid.classList.add('actual-content-visible');

                offersGrid.classList.remove('actual-content-hidden');
                offersGrid.classList.add('actual-content-visible');

                restaurantsGrid.classList.remove('actual-content-hidden'); // Show restaurants grid
                restaurantsGrid.classList.add('actual-content-visible');


                // Show actual headings
                categoriesHeading.classList.remove('actual-content-hidden');
                categoriesHeading.classList.add('actual-content-visible');
                offersHeading.classList.remove('actual-content-hidden');
                offersHeading.classList.add('actual-content-visible');
                restaurantsHeading.classList.remove('actual-content-hidden'); // Show restaurants heading
                restaurantsHeading.classList.add('actual-content-visible');
            };

            // Check if content was already loaded in this session
            // As per user request, we will always treat it as a fresh load for content display
            // Check if we have preloaded data for instant loading
            if (hasPreloadedData && isUsingPreloadedData) {
                // Show content immediately without skeletons for preloaded sections
                hideAllSkeletons();
                showAllContent();

                // Load content using preloaded data (will be instant)
                updateProfilePicture();
                fetchCategoriesAndDisplay();
                fetchAndDisplayFoodItems(currentActiveCategoryName, foodItemsGrid);
                fetchAndDisplayOffers(offersGrid, offersGridSkeleton, offersHeading);
                fetchAndDisplayRestaurants();
            } else {

                // Standard loading with skeletons
                // Initial state: hide actual content and show skeletons
                categoriesGrid.classList.add('actual-content-hidden');
                foodItemsGrid.classList.add('actual-content-hidden');
                offersGrid.classList.add('actual-content-hidden');
                restaurantsGrid.classList.add('actual-content-hidden'); // Hide restaurants grid


                // Hide actual headings
                categoriesHeading.classList.add('actual-content-hidden');
                offersHeading.classList.add('actual-content-hidden');
                restaurantsHeading.classList.add('actual-content-hidden'); // Hide restaurants heading

                // Dynamically generate skeletons and append them
                categoriesGridSkeleton.appendChild(generateCategorySkeletons(4)); // Example: 4 category skeletons
                foodItemsGridSkeleton.appendChild(generateFoodCardSkeletons(4)); // Example: 4 food card skeletons
                offersGridSkeleton.appendChild(generateOfferBannerSkeletons(2)); // Example: 2 offer skeletons
                restaurantsGridSkeleton.appendChild(generateRestaurantCardSkeletons(4)); // Example: 4 restaurant skeletons


                // OPTIMIZED: Load all critical content in parallel for faster initial load
                const [currencyResult, categoriesResult] = await Promise.allSettled([
                    fetchCurrencySymbol(),
                    fetchCategoriesAndDisplay()
                ]);

                // Start loading food items and profile picture in parallel
                const parallelPromises = [
                    updateProfilePicture(),
                    fetchAndDisplayFoodItems(currentActiveCategoryName, foodItemsGrid)
                ];

                // Wait for the parallel promises to complete
                const [profileResult, foodItemPromises] = await Promise.allSettled(parallelPromises);

                // Show categories immediately if loaded successfully
                if (categoriesResult.status === 'fulfilled') {
                    categoriesGridSkeleton.classList.add('hidden');
                    categoriesGrid.classList.remove('actual-content-hidden');
                    categoriesGrid.classList.add('actual-content-visible');
                    categoriesHeading.classList.remove('actual-content-hidden');
                    categoriesHeading.classList.add('actual-content-visible');
                }

                // Show food items immediately if loaded successfully
                if (foodItemPromises.status === 'fulfilled') {
                    foodItemsGridSkeleton.classList.add('hidden');
                    foodItemsGrid.classList.remove('actual-content-hidden');
                    foodItemsGrid.classList.add('actual-content-visible');
                }

                // Load offers and restaurants in the background (non-blocking)
                Promise.allSettled([
                    fetchAndDisplayOffers(offersGrid, offersGridSkeleton, offersHeading),
                    fetchAndDisplayRestaurants()
                ]).then(([offersResult, restaurantsResult]) => {
                    // Handle offers result
                    if (offersResult.status === 'fulfilled') {
                        Promise.all(offersResult.value || []).then(() => {
                            offersGridSkeleton.classList.add('hidden');
                            offersGrid.classList.remove('actual-content-hidden');
                            offersGrid.classList.add('actual-content-visible');
                            offersHeading.classList.remove('actual-content-hidden');
                            offersHeading.classList.add('actual-content-visible');
                        }).catch(() => {
                            // Show offers section even if images fail
                            offersGridSkeleton.classList.add('hidden');
                            offersGrid.classList.remove('actual-content-hidden');
                            offersGrid.classList.add('actual-content-visible');
                            offersHeading.classList.remove('actual-content-hidden');
                            offersHeading.classList.add('actual-content-visible');
                        });
                    }
                }).catch(error => {
                    console.warn("Background content loading failed:", error);
                    // Still show the sections even if background loading fails
                    hideAllSkeletons();
                    showAllContent();
                });
            }


            // Authentication is now handled by the new secure cookie system in initializeApp()
            // No need for old localStorage-based checks

            // Unified navigation item click handler
            allNavItems.forEach(item => {
                item.addEventListener('click', (event) => {
                    event.preventDefault(); // Prevent default link behavior
                    allNavItems.forEach(nav => nav.classList.remove('active-link')); // Remove active from ALL nav items
                    item.classList.add('active-link'); // Add active to clicked item

                    // No message box calls here as per user request
                });
            });

            // Handle Profile Nav Item click (with new authentication check)
            profileNavItem.addEventListener('click', async (event) => {
                event.preventDefault(); // Prevent default link behavior
                allNavItems.forEach(nav => nav.classList.remove('active-link')); // Ensure all others are inactive
                profileNavItem.classList.add('active-link'); // Set profile as active

                // Check authentication using the new secure cookie system
                const isAuthenticated = await checkSession();

                if (isAuthenticated && currentUser) {
                    showMessageBox(`Welcome to your profile, ${currentUser.name || currentUser.email}!`, [{ text: 'OK', callback: null, className: 'btn-primary-popup'}]);
                } else {
                    // For guest users, show a different message
                    showMessageBox('Profile features are available for registered users. Would you like to sign up?', [
                        { text: 'Sign Up', callback: () => window.location.href = '/login.html', className: 'btn-primary-popup'},
                        { text: 'Maybe Later', callback: null, className: 'btn-secondary-popup'}
                    ]);
                }
            });

            // Service worker registration (kept as it's a core PWA feature)
            if ('serviceWorker' in navigator) {
                window.addEventListener('load', () => {
                    navigator.serviceWorker.register('/service-worker.js')
                        .then(registration => {
                        })
                        .catch(error => {
                        });
                });
            }

            // Get initial computed styles for header animation
            const initialHeaderHeight = appHeader.offsetHeight;
            const initialGreetingFontSize = parseFloat(getComputedStyle(headerGreeting).fontSize);
            const initialGreetingMarginTop = parseFloat(getComputedStyle(headerGreeting).marginTop);
            const initialGreetingMarginBottom = parseFloat(getComputedStyle(headerGreeting).marginBottom);
            const initialSearchBarPaddingTop = parseFloat(getComputedStyle(headerSearchBar).paddingTop);
            const initialSearchBarPaddingBottom = parseFloat(getComputedStyle(headerSearchBar).paddingBottom);

            // Define target values for fully scrolled state (in px or unit-less for scale/opacity)
            const targetHeaderHeight = 120; // px
            const targetGreetingFontSize = 0; // px
            const targetGreetingMarginTop = 0; // px
            const targetGreetingMarginBottom = 0; // px
            const targetGreetingTransformY = -20; // px
            const targetSearchBarPaddingTop = 0; // px
            const targetSearchBarPaddingBottom = 0; // px
            const targetSearchBarTransformY = -20; // px
            const targetIconScaleHidden = 0.8;
            const targetIconScaleVisible = 1;

            const scrollAnimationEnd = 400; // Increased for smoother, longer transition

            // Helper function for linear interpolation
            const lerp = (start, end, progress) => start + (end - start) * progress;

            window.addEventListener('scroll', () => {
                const currentScrollY = window.scrollY;
                // Calculate scroll progress (0 to 1), clamped to prevent values outside the range
                const scrollProgress = Math.min(1, currentScrollY / scrollAnimationEnd);

                // Interpolate header properties
                const newHeaderHeight = lerp(initialHeaderHeight, targetHeaderHeight, scrollProgress);

                appHeader.style.height = `${newHeaderHeight}px`;
                // Apply final border-radius when fully scrolled
                if (scrollProgress === 1) {
                    appHeader.classList.add('header-scrolled');
                } else {
                    appHeader.classList.remove('header-scrolled');
                }

                // Interpolate greeting properties
                const newGreetingFontSize = lerp(initialGreetingFontSize, targetGreetingFontSize, scrollProgress);
                const newGreetingOpacity = lerp(1, 0, scrollProgress);
                const newGreetingTransformY = lerp(0, targetGreetingTransformY, scrollProgress);
                const newGreetingMarginTop = lerp(initialGreetingMarginTop, targetGreetingMarginTop, scrollProgress);
                const newGreetingMarginBottom = lerp(initialGreetingMarginBottom, targetGreetingMarginBottom, scrollProgress);


                headerGreeting.style.fontSize = `${newGreetingFontSize}px`;
                headerGreeting.style.opacity = newGreetingOpacity;
                headerGreeting.style.transform = `translateY(${newGreetingTransformY}px)`;
                headerGreeting.style.marginTop = `${newGreetingMarginTop}px`;
                headerGreeting.style.marginBottom = `${newGreetingMarginBottom}px`;
                // Hide completely if font size is tiny or opacity is very low
                if (newGreetingFontSize <= 1 || newGreetingOpacity <= 0.05) {
                    headerGreeting.style.display = 'none';
                } else {
                    headerGreeting.style.display = 'block';
                }


                // Interpolate search bar properties
                const newSearchBarPaddingTop = lerp(initialSearchBarPaddingTop, targetSearchBarPaddingTop, scrollProgress);
                const newSearchBarPaddingBottom = lerp(initialSearchBarPaddingBottom, targetSearchBarPaddingBottom, scrollProgress);
                const newSearchBarOpacity = lerp(1, 0, scrollProgress);
                const newSearchBarTransformY = lerp(0, targetSearchBarTransformY, scrollProgress);

                headerSearchBar.style.paddingTop = `${newSearchBarPaddingTop}px`;
                headerSearchBar.style.paddingBottom = `${newSearchBarPaddingBottom}px`;
                headerSearchBar.style.opacity = newSearchBarOpacity;
                headerSearchBar.style.transform = `translateY(${newSearchBarTransformY}px)`;
                if (newSearchBarOpacity <= 0.05) {
                    headerSearchBar.style.display = 'none';
                } else {
                    headerSearchBar.style.display = 'flex'; // It's a flex container
                }


                // Interpolate icon properties
                const newNotificationIconOpacity = lerp(1, 0, scrollProgress);
                const newNotificationIconScale = lerp(targetIconScaleVisible, targetIconScaleHidden, scrollProgress); // From 1 to 0.8
                const newSearchIconOpacity = lerp(0, 1, scrollProgress);
                const newSearchIconScale = lerp(targetIconScaleHidden, targetIconScaleVisible, scrollProgress); // From 0.8 to 1

                notificationIcon.style.opacity = newNotificationIconOpacity;
                notificationIcon.style.transform = `scale(${newNotificationIconScale})`;

                // Adjust z-index to prevent flickering during icon transition
                if (newNotificationIconOpacity < 0.1 && newSearchIconOpacity > 0.1) {
                    // Notification icon is mostly hidden, search icon is mostly visible
                    notificationIcon.style.zIndex = 5;
                    smallSearchIcon.style.zIndex = 10;
                } else {
                    // Notification icon is visible, or both are in transition (default to notification on top)
                    notificationIcon.style.zIndex = 10;
                    smallSearchIcon.style.zIndex = 5;
                }

                smallSearchIcon.style.opacity = newSearchIconOpacity;
                smallSearchIcon.style.transform = `scale(${newSearchIconScale})`;

                // Manage pointer-events to disable clicks on hidden icons
                if (newNotificationIconOpacity <= 0.05) {
                    notificationIcon.style.pointerEvents = 'none';
                } else {
                    notificationIcon.style.pointerEvents = 'auto';
                }

                if (newSearchIconOpacity >= 0.95) {
                    smallSearchIcon.style.pointerEvents = 'auto';
                } else {
                    smallSearchIcon.style.pointerEvents = 'none';
                }

            });





            // Initial call to set styles based on current scroll position (useful on page refresh)
            window.dispatchEvent(new Event('scroll'));

            // Call getUserLocation on page load and then fetch restaurants
            getUserLocation().then(() => {
                // Location is now available (userLatitude, userLongitude are set, either actual or default)
                // Proceed with fetching and displaying restaurants if content isn't already loaded
                if (!hasLoadedContent) {
                    fetchAndDisplayRestaurants(); // This function now handles its own skeleton logic
                }
            }).catch(error => {
                console.warn("Error during initial getUserLocation call, but proceeding with default location:", error);
                // This catch block will only be hit if the getUserLocation promise itself rejects,
                // which it's now designed not to do. But keeping for robustness.
                if (!hasLoadedContent) {
                    fetchAndDisplayRestaurants(); // Proceed with default location
                }
            });

            // Prevent elastic scroll on iOS
            function preventElasticScroll() {
                let startY = 0;

                document.addEventListener('touchstart', function(e) {
                    startY = e.touches[0].pageY;
                }, { passive: false });

                document.addEventListener('touchmove', function(e) {
                    const y = e.touches[0].pageY;
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    const scrollHeight = document.documentElement.scrollHeight;
                    const clientHeight = window.innerHeight;

                    // Prevent scrolling past the top
                    if (scrollTop <= 0 && y > startY) {
                        e.preventDefault();
                    }

                    // Prevent scrolling past the bottom
                    if (scrollTop + clientHeight >= scrollHeight && y < startY) {
                        e.preventDefault();
                    }
                }, { passive: false });
            }

            // Prevent iOS Safari auto-scroll on input focus
            function preventAutoScroll() {
                const inputs = document.querySelectorAll('input, textarea');

                inputs.forEach(input => {
                    input.addEventListener('focus', function(e) {
                        // Store current scroll position
                        const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;

                        // Small delay to ensure the keyboard is shown, then restore position
                        setTimeout(() => {
                            // Restore the scroll position
                            window.scrollTo(0, currentScrollTop);
                        }, 100);
                    }, { passive: true });

                    input.addEventListener('blur', function() {
                        // Ensure scroll position stays controlled when input loses focus
                        setTimeout(() => {
                            const currentScrollTop = window.pageYOffset || document.documentElement.scrollTop;
                            window.scrollTo(0, currentScrollTop);
                        }, 100);
                    });
                });
            }

            // Initialize prevention functions
            preventElasticScroll();
            preventAutoScroll();

            // Re-initialize when new inputs are added (for dynamic content)
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        preventAutoScroll();
                    }
                });
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            // Lazy loading implementation for images
            const lazyLoadImages = () => {
                const imageObserver = new IntersectionObserver((entries, observer) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            const img = entry.target;
                            const src = img.getAttribute('data-src');
                            if (src) {
                                img.src = src;
                                img.classList.remove('lazy-load');
                                observer.unobserve(img);
                            }
                        }
                    });
                }, {
                    rootMargin: '50px 0px', // Start loading 50px before the image comes into view
                    threshold: 0.1
                });

                // Observe all lazy-load images
                document.querySelectorAll('.lazy-load').forEach(img => {
                    imageObserver.observe(img);
                });

                // Re-observe new images when content is added
                const contentObserver = new MutationObserver(mutations => {
                    mutations.forEach(mutation => {
                        mutation.addedNodes.forEach(node => {
                            if (node.nodeType === 1) { // Element node
                                const lazyImages = node.querySelectorAll ? node.querySelectorAll('.lazy-load') : [];
                                lazyImages.forEach(img => imageObserver.observe(img));

                                // Also check if the node itself is a lazy-load image
                                if (node.classList && node.classList.contains('lazy-load')) {
                                    imageObserver.observe(node);
                                }
                            }
                        });
                    });
                });

                contentObserver.observe(document.body, {
                    childList: true,
                    subtree: true
                });
            };

            // Initialize lazy loading
            lazyLoadImages();


        });
    </script>
    <script src="/js/qr.js"></script>

    <!-- Leaflet JavaScript -->
    <script src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
            integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
            crossorigin=""></script>
</body>
</html>
