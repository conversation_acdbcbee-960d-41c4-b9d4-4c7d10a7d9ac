<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0">
    <title>MIKO - Food Delivery</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <meta name="theme-color" content="#ff5018" id="themeColorMeta">
    <link rel="icon" href="/icons/icon.png">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.2/css/all.min.css" xintegrity="sha512-SnH5WK+bZxgPHs44uWIX+LLJAJ9/2PkPKZ5QiAj6Ta86w+fsb2TkcmfRyVX3pBnMFcV7oQPJkl9QevSCWr3W6A==" crossorigin="anonymous" referrerpolicy="no-referrer" />
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600;700&display=swap" rel="stylesheet">
    <style>
        /* iOS PWA optimizations */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;

            /* Prevent touch gestures and zoom */
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        html {
            touch-action: manipulation;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            overscroll-behavior: none;
        }

        body {
            font-family: "Poppins", sans-serif;
            height: 100vh;
            width: 100vw;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            background-color: var(--primary-accent);
            transition: background-color 0.5s ease-in-out;

            /* Prevent elastic scroll and touch gestures */
            touch-action: manipulation;
            overscroll-behavior: none;
            -webkit-overflow-scrolling: touch;
            position: fixed;
            top: 0;
            left: 0;
        }

        body.bg-white {
            background-color: var(--pure-white);
        }

        :root {
            --primary-accent: #ff5018;
            --secondary-accent: #FF7F50;
            --neutral-base: #F7F7F7;
            --text-dark-elements: #333333;
            --subtle-detail: #A7A7A7;
            --pure-white: #FFFFFF;
            --green-button: #4CAF50;
            --green-button-hover: #45a049;
            --light-green-bg: #e6ffe6;
            --dark-green-icon: #2e7d32;
            --error-red: #dc3545;
            --disabled-grey: #6c757d;
        }
        .text-primary-accent {
            color: var(--primary-accent);
        }

        .splash-screen-wrapper {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            z-index: 9999;
            background-color: transparent;
            transition: opacity 0.5s ease-out;
        }
        .splash-screen-wrapper.fade-out-splash {
            opacity: 0;
            pointer-events: none;
        }
        .splash-top-section {
            flex-grow: 1;
            background-color: var(--primary-accent);
            color: var(--pure-white);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            position: relative;
            overflow: hidden;
            padding-bottom: 50px;
            transition: all 0.5s ease-in-out;
        }
        .splash-top-section.full-screen-center {
            flex-grow: 1;
            padding-bottom: 0;
        }
        .splash-pattern-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg width='200' height='200' viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='foodPattern' x='0' y='0' width='200' height='200' patternUnits='userSpaceOnUse'%3E%3Cg fill='none' stroke='white' stroke-width='1.5' opacity='0.2'%3E%3C!-- Tomato/Round Item --%3E%3Ccircle cx='40' cy='40' r='18'/%3E%3Cpath d='M40 22 L40 58 M22 40 L58 40'/%3E%3C!-- Chili Pepper 1 --%3E%3Cpath d='M160 30 Q175 15, 185 30 T190 50 T180 70 T160 60 Z'/%3E%3C!-- Cucumber Slice --%3E%3Ccircle cx='140' cy='150' r='15'/%3E%3Cpath d='M140 135 L140 165 M125 150 L155 150'/%3E%3C!-- Leaf --%3E%3Cpath d='M60 110 Q70 100, 80 110 T90 120 T80 130 T70 120 T60 110 Z'/%3E%3C!-- Chili Pepper 2 (rotated) --%3E%3Cg transform='translate(20, 100) rotate(90)'%3E%3Cpath d='M160 30 Q175 15, 185 30 T190 50 T180 70 T160 60 Z'/%3E%3C/g%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect x='0' y='0' width='100%25' height='100%25' fill='url(%23foodPattern)'/%3E%3C/svg%3E");
            background-size: 200px 200px;
            background-repeat: repeat;
            z-index: 0;
            transition: opacity 0.5s ease-out;
        }
        .splash-pattern-overlay.fade-out {
            opacity: 0;
        }
        .logo-container {
            width: 120px;
            height: 120px;
            background: var(--neutral-base);
            border-radius: 30px;
            margin-bottom: 0px;
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
            z-index: 2;
            opacity: 1;
            pointer-events: auto;
            transition: opacity 0.5s ease-out;
        }
        .logo-container.logo-fade-out {
            opacity: 0;
        }
        .logo {
            font-size: 70px;
            color: var(--primary-accent);
        }
        .splash-bottom-section {
            background-color: var(--pure-white);
            border-top-left-radius: 60px;
            border-top-right-radius: 60px;
            padding: 2rem;
            text-align: center;
            box-shadow: 0 -4px 12px rgba(0,0,0,0.08);
            position: relative;
            z-index: 2;
            margin-top: -60px;
            min-height: 400px;
            display: flex;
            flex-direction: column;
            justify-content: flex-end;
            align-items: center;
            width: 100%;
            padding-bottom: 0;
            transition: transform 0.5s ease-in-out;
        }
        .splash-bottom-section.slide-down {
            transform: translateY(100vh);
        }
        .splash-tagline {
            font-size: 2.5rem;
            font-weight: 700;
            color: var(--text-dark-elements);
            margin-bottom: 0.5rem;
            line-height: 1.2;
        }
        .splash-description {
            font-size: 1.1rem;
            color: var(--subtle-detail);
            margin-bottom: 4rem;
            max-width: 85%;
            margin-left: auto;
            margin-right: auto;
        }
        .button-container {
            width: 100%;
            padding: 0 2rem;
            padding-bottom: 2rem;
            box-sizing: border-box;
            margin-top: 2rem;
        }
        .continue-button {
            background-color: var(--primary-accent);
            color: var(--pure-white);
            padding: 1rem 0;
            border: none;
            border-radius: 9999px;
            font-size: 1.2rem;
            font-weight: 600;
            cursor: pointer;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
            transition: background-color 0.3s ease, transform 0.2s ease;
            width: 100%;
        }
        .continue-button:hover {
            background-color: var(--secondary-accent);
            transform: translateY(-2px);
        }

        .post-splash-content {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: 2rem;
            max-width: 468px;
            width: 100%;
            text-align: center;
            opacity: 0;
            pointer-events: none;
            transition: opacity 0.5s ease-in-out;
        }
        .post-splash-content.active {
            opacity: 1;
            pointer-events: auto;
        }

        @keyframes squeezeOut {
            0% {
                opacity: 1;
                transform: scale(1);
            }
            100% {
                opacity: 0;
                transform: scale(0.8);
            }
        }

        .post-splash-content.squeeze-out {
            animation: squeezeOut 0.4s ease-out forwards;
            pointer-events: none;
        }

        .location-image {
            width: 468px;
            height: auto;
            object-fit: contain;
            margin-bottom: 1.5rem;
        }

        .main-heading {
            font-size: 2rem;
            font-weight: 700;
            color: var(--text-dark-elements);
            margin-bottom: 0.5rem;
        }
        .sub-text {
            font-size: 0.9rem;
            color: var(--subtle-detail);
            margin-bottom: 2rem;
            line-height: 1.5;
        }

        .permission-info {
            display: flex;
            align-items: center;
            gap: 1rem;
            margin-bottom: 2rem;
            width: 100%;
            justify-content: center;
            padding: 0 1.5rem;
        }

        .permission-icon {
            color: var(--subtle-detail);
            font-size: 1.8rem;
            flex-shrink: 0;
        }
        .permission-text h3 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--text-dark-elements);
            margin-bottom: 0.2rem;
        }
        .permission-text p {
            font-size: 0.85rem;
            color: var(--subtle-detail);
            line-height: 1.3;
        }

        .action-buttons-container {
            display: flex;
            justify-content: center;
            width: 100%;
            max-width: 300px;
            margin-bottom: 0.5rem;
        }

        .action-button {
            background-color: var(--primary-accent);
            color: var(--pure-white);
            padding: 1rem 2rem;
            border: none;
            border-radius: 9999px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            width: 100%;
            max-width: 300px;
            transition: background-color 0.3s ease, transform 0.2s ease, width 0.3s ease, height 0.3s ease, border-radius 0.3s ease, padding 0.3s ease;
            position: relative;
            overflow: hidden;
            display: flex;
            justify-content: center;
            align-items: center;
        }
        .action-button:hover {
            background-color: var(--secondary-accent);
            transform: translateY(-2px);
        }
        .action-button:disabled {
            cursor: not-allowed;
            opacity: 0.8;
        }
        .action-button:disabled:hover {
            background-color: var(--primary-accent);
            transform: translateY(0);
        }

        .action-button .button-text {
            transition: opacity 0.3s ease, transform 0.3s ease;
            white-space: nowrap;
        }

        .action-button .tick-icon {
            opacity: 0;
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%) scale(0.5);
            transition: opacity 0.3s ease, transform 0.3s ease, font-size 0.3s ease;
            font-size: 0;
        }

        .action-button.location-granted {
            width: 60px;
            height: 60px;
            border-radius: 50%;
            padding: 0;
            background-color: var(--primary-accent);
            pointer-events: none;
            cursor: default;
        }
        .action-button.location-granted .button-text {
            opacity: 0;
            transform: scale(0);
        }
        .action-button.location-granted .tick-icon {
            opacity: 1;
            transform: translate(-50%, -50%) scale(1);
            font-size: 1.8rem;
        }

        .action-button.location-denied-error {
            background-color: var(--error-red);
        }
        .action-button.location-unsupported {
            background-color: var(--disabled-grey);
        }

        .location-status-message {
            font-size: 0.85rem;
            color: var(--subtle-detail);
            margin-top: 0.5rem;
            min-height: 1.5em;
            text-align: center;
            width: 100%;
            max-width: 300px;
        }

        .final-transition-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            background-color: transparent;
            z-index: 10000;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.4s ease-out, background-color 0.4s ease-out;
        }

        .final-transition-overlay.active {
            opacity: 1;
            visibility: visible;
        }

        .final-transition-overlay.bg-success {
            background-color: var(--green-button);
        }

        .final-transition-overlay.bg-error {
            background-color: var(--error-red);
        }

        .final-icon {
            font-size: 0;
            opacity: 0;
            transform: translateY(-50px) scale(0.5);
            transition: opacity 0.5s ease-out, transform 0.5s ease-out;
        }

        .final-transition-overlay.bg-success #successIcon {
            color: var(--pure-white);
        }
        .final-transition-overlay.bg-error #deniedIcon {
            color: var(--pure-white);
        }

        @keyframes dropIn {
            0% {
                opacity: 0;
                transform: translateY(-50px) scale(0.5);
            }
            70% {
                opacity: 1;
                transform: translateY(5px) scale(1.1);
            }
            100% {
                opacity: 1;
                transform: translateY(0) scale(1);
            }
        }

        .final-icon.animate-drop-in {
            font-size: 6rem;
            animation: dropIn 0.7s cubic-bezier(0.68, -0.55, 0.27, 1.55) forwards;
        }
    </style>
</head>
<body>
    <div id="splash-screen" class="splash-screen-wrapper">
        <div id="splashTopSection" class="splash-top-section">
            <div id="splashPatternOverlay" class="splash-pattern-overlay"></div>
            <div class="logo-container">
                <i class="fas fa-utensils logo"></i>
            </div>
        </div>
        <div id="splashBottomSection" class="splash-bottom-section">
            <h2 id="splashTagline" class="splash-tagline"><span class="text-primary-accent">Hungry?</span> Get It Fast</h2>
            <p id="splashDescription" class="splash-description">Fast, fresh, and just the way you like it!</p>
            <div id="buttonContainer" class="button-container">
                <button id="continueButton" class="continue-button">
                    Continue
                </button>
            </div>
        </div>
    </div>

    <div id="postSplashContent" class="post-splash-content">
        <!-- Location Permission Section -->
        <section id="location">
        <img src="/images/location.png" alt="Location Icon" class="location-image" onerror="this.onerror=null; this.src='https://placehold.co/468xauto/e6ffe6/2e7d32?text=Location'">
        
        <h1 class="main-heading">One last step</h1>
        <p class="sub-text">To get the most joy out of MIKO, you'll need to allow this following</p>

        <div class="permission-info">
            <i class="fas fa-map-marker-alt permission-icon"></i>
            <div class="permission-text">
                <h3>Location</h3>
                <p>Share location to get nearby restaurants</p>
            </div>
        </div>
        </section> <!-- End location section -->

        <div class="action-buttons-container">
            <button class="action-button" id="postSplashContinueButton">
                <span class="button-text">Continue</span>
                <i class="fa-regular fa-circle-check tick-icon"></i>
            </button>
        </div>
        <p id="locationErrorMessage" class="location-status-message"></p>
    </div>

    <div id="finalTransitionOverlay" class="final-transition-overlay">
        <i id="successIcon" class="fa-regular fa-circle-check final-icon"></i>
        <i id="deniedIcon" class="fa-regular fa-circle-xmark final-icon"></i>
    </div>

    <script>
        function setVisitingCustomerFlag(value) {
            return new Promise((resolve, reject) => {
                const request = indexedDB.open('MikoDB', 1);

                request.onupgradeneeded = function(event) {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains('settings')) {
                        db.createObjectStore('settings', { keyPath: 'id' });
                    }
                };

                request.onsuccess = function(event) {
                    const db = event.target.result;
                    const transaction = db.transaction(['settings'], 'readwrite');
                    const objectStore = transaction.objectStore('settings');
                    const putRequest = objectStore.put({ id: 'isVisitingCustomer', value: value });

                    putRequest.onsuccess = function() {
                        resolve();
                    };

                    putRequest.onerror = function(event) {
                        reject(event.target.errorCode);
                    };

                    transaction.onerror = function(event) {
                        reject(event.target.errorCode);
                    };
                };

                request.onerror = function(event) {
                    reject(event.target.errorCode);
                };
            });
        }

        document.addEventListener('DOMContentLoaded', async () => {
            history.pushState(null, document.title, location.href);
            window.addEventListener('popstate', function (event) {
                history.pushState(null, document.title, location.href);
            });

            // Check if user has any authentication - if not, create guest session
            await ensureGuestSession();

            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/service-worker.js')
                    .then(registration => {
                    })
                    .catch(error => {
                    });
            }
            
            const splashScreen = document.getElementById('splash-screen');
            const splashTopSection = document.getElementById('splashTopSection');
            const splashBottomSection = document.getElementById('splashBottomSection');
            const continueButton = document.getElementById('continueButton');
            const splashPatternOverlay = document.getElementById('splashPatternOverlay');
            const logoContainer = document.querySelector('.logo-container');
            const postSplashContent = document.getElementById('postSplashContent');
            const postSplashContinueButton = document.getElementById('postSplashContinueButton');
            const postSplashButtonText = postSplashContinueButton ? postSplashContinueButton.querySelector('.button-text') : null;
            const locationErrorMessage = document.getElementById('locationErrorMessage');
            const finalTransitionOverlay = document.getElementById('finalTransitionOverlay');
            const successIcon = document.getElementById('successIcon');
            const deniedIcon = document.getElementById('deniedIcon');
            const themeColorMeta = document.getElementById('themeColorMeta');

            const squeezeOutAnimationDuration = 400;
            const dropInAnimationDuration = 700;
            const finalScreenDisplayDuration = 1000;

            const getCssVariable = (variableName) => {
                return getComputedStyle(document.documentElement).getPropertyValue(variableName).trim();
            };

            if (continueButton) {
                continueButton.addEventListener('click', () => {
                    setTimeout(() => {
                        splashBottomSection.classList.add('slide-down');
                        splashTopSection.classList.add('full-screen-center');
                        
                        if (splashPatternOverlay) {
                            splashPatternOverlay.classList.add('fade-out');
                        }
                        
                        if (logoContainer) {
                            logoContainer.classList.add('logo-fade-out');
                        }

                        setTimeout(() => {
                            splashScreen.classList.add('fade-out-splash');
                            
                            setTimeout(() => {
                                postSplashContent.classList.add('active');
                                if (themeColorMeta) {
                                    themeColorMeta.setAttribute('content', '#FFFFFF');
                                }
                                document.body.classList.add('bg-white');
                            }, 500);
                        }, 500);
                }, 300);
                });
            } else {
                splashScreen.classList.add('fade-out-splash');
                setTimeout(() => {
                    postSplashContent.classList.add('active');
                    if (themeColorMeta) {
                        themeColorMeta.setAttribute('content', '#FFFFFF');
                    }
                    document.body.classList.add('bg-white');
                }, 500);
            }

            const requestLocation = () => {
                postSplashContinueButton.disabled = false;
                postSplashContinueButton.style.cursor = 'pointer';
                postSplashContinueButton.classList.remove('location-granted', 'location-denied-error', 'location-unsupported');
                if (postSplashButtonText) {
                    postSplashButtonText.textContent = "Continue";
                }
                locationErrorMessage.textContent = '';

                if (navigator.geolocation) {
                    navigator.geolocation.getCurrentPosition(
                        async (position) => {
                            postSplashContinueButton.classList.add('location-granted');
                            postSplashContinueButton.disabled = true;
                            postSplashContinueButton.style.cursor = 'default';

                            try {
                                await setVisitingCustomerFlag(true);
                                postSplashContent.classList.add('squeeze-out');
                                
                                setTimeout(() => {
                                    finalTransitionOverlay.classList.add('active', 'bg-success');
                                    document.body.classList.remove('bg-white');
                                    document.body.style.backgroundColor = getCssVariable('--green-button');
                                    
                                    finalTransitionOverlay.addEventListener('transitionend', function handler(event) {
                                        if (event.propertyName === 'background-color') {
                                            if (themeColorMeta) {
                                                themeColorMeta.setAttribute('content', getCssVariable('--green-button'));
                                            }
                                            successIcon.style.display = 'block';
                                            deniedIcon.style.display = 'none';
                                            successIcon.classList.add('animate-drop-in');
                                            
                                            finalTransitionOverlay.removeEventListener('transitionend', handler);

                                            setTimeout(async () => {
                                                await completeGuestSetup(true); // Location granted
                                                window.location.replace('/home.html');
                                            }, dropInAnimationDuration + finalScreenDisplayDuration);
                                        }
                                    }, { once: true });

                                }, squeezeOutAnimationDuration);

                            } catch (error) {
                                postSplashContent.classList.add('squeeze-out');
                                setTimeout(() => {
                                    finalTransitionOverlay.classList.add('active', 'bg-success');
                                    document.body.classList.remove('bg-white');
                                    document.body.style.backgroundColor = getCssVariable('--green-button');
                                    finalTransitionOverlay.addEventListener('transitionend', function handler(event) {
                                        if (event.propertyName === 'background-color') {
                                            if (themeColorMeta) {
                                                themeColorMeta.setAttribute('content', getCssVariable('--green-button'));
                                            }
                                            successIcon.style.display = 'block';
                                            deniedIcon.style.display = 'none';
                                            successIcon.classList.add('animate-drop-in');
                                            finalTransitionOverlay.removeEventListener('transitionend', handler);
                                            setTimeout(async () => {
                                                await completeGuestSetup(true); // Location granted
                                                window.location.replace('/home.html');
                                            }, dropInAnimationDuration + finalScreenDisplayDuration);
                                        }
                                    }, { once: true });
                                }, squeezeOutAnimationDuration);
                            }
                        },
                        async (error) => {
                            postSplashContinueButton.classList.add('location-denied-error');

                            if (postSplashButtonText) {
                                postSplashButtonText.textContent = "Proceed without Location";
                            }
                            locationErrorMessage.textContent = "Could not get location. You can proceed without it.";
                            
                            postSplashContinueButton.onclick = async () => {
                                try {
                                    await setVisitingCustomerFlag(true);
                                } catch (err) {
                                }
                                postSplashContent.classList.add('squeeze-out');
                                
                                setTimeout(() => {
                                    finalTransitionOverlay.classList.add('active', 'bg-error');
                                    document.body.classList.remove('bg-white');
                                    document.body.style.backgroundColor = getCssVariable('--error-red');
                                    
                                    finalTransitionOverlay.addEventListener('transitionend', function handler(event) {
                                        if (event.propertyName === 'background-color') {
                                            if (themeColorMeta) {
                                                themeColorMeta.setAttribute('content', getCssVariable('--error-red'));
                                            }
                                            successIcon.style.display = 'none';
                                            deniedIcon.style.display = 'block';
                                            deniedIcon.classList.add('animate-drop-in');
                                            
                                            finalTransitionOverlay.removeEventListener('transitionend', handler);
                                            setTimeout(async () => {
                                                await completeGuestSetup(false); // Location denied
                                                window.location.replace('/home.html');
                                            }, dropInAnimationDuration + finalScreenDisplayDuration);
                                        }
                                    }, { once: true });
                                }, squeezeOutAnimationDuration);
                            };

                            postSplashContinueButton.disabled = false;
                            postSplashContinueButton.style.cursor = 'pointer';
                        },
                        {
                            enableHighAccuracy: true,
                            timeout: 5000,
                            maximumAge: 0
                        }
                    );
                } else {
                    postSplashContinueButton.textContent = "Not Supported";
                    locationErrorMessage.textContent = "Geolocation is not supported by your browser. Some features will be unavailable.";
                    postSplashContinueButton.classList.add('location-unsupported');
                    postSplashContinueButton.disabled = true;
                    postSplashContinueButton.style.cursor = 'not-allowed';

                    setVisitingCustomerFlag(true)
                        .then(() => {
                            postSplashContent.classList.add('squeeze-out');
                            setTimeout(() => {
                                finalTransitionOverlay.classList.add('active', 'bg-error');
                                document.body.classList.remove('bg-white');
                                document.body.style.backgroundColor = getCssVariable('--error-red');
                                finalTransitionOverlay.addEventListener('transitionend', function handler(event) {
                                    if (event.propertyName === 'background-color') {
                                        if (themeColorMeta) {
                                            themeColorMeta.setAttribute('content', getCssVariable('--error-red'));
                                        }
                                        successIcon.style.display = 'none';
                                        deniedIcon.style.display = 'block';
                                        deniedIcon.classList.add('animate-drop-in');
                                        finalTransitionOverlay.removeEventListener('transitionend', handler);
                                        setTimeout(async () => {
                                            await completeGuestSetup(false); // Location denied
                                            window.location.replace('/home.html');
                                        }, dropInAnimationDuration + finalScreenDisplayDuration);
                                    }
                                }, { once: true });
                            }, squeezeOutAnimationDuration);
                        })
                        .catch(error => {
                            postSplashContent.classList.add('squeeze-out');
                            setTimeout(() => {
                                finalTransitionOverlay.classList.add('active', 'bg-error');
                                document.body.classList.remove('bg-white');
                                document.body.style.backgroundColor = getCssVariable('--error-red');
                                finalTransitionOverlay.addEventListener('transitionend', function handler(event) {
                                    if (event.propertyName === 'background-color') {
                                        if (themeColorMeta) {
                                            themeColorMeta.setAttribute('content', getCssVariable('--error-red'));
                                        }
                                        successIcon.style.display = 'none';
                                        deniedIcon.style.display = 'block';
                                        deniedIcon.classList.add('animate-drop-in');
                                        finalTransitionOverlay.removeEventListener('transitionend', handler);
                                        setTimeout(async () => {
                                            await completeGuestSetup(false); // Location denied
                                            window.location.replace('/home.html');
                                        }, dropInAnimationDuration + finalScreenDisplayDuration);
                                    }
                                }, { once: true });
                            }, squeezeOutAnimationDuration);
                        });
                }
            };

            if (postSplashContinueButton) {
                postSplashContinueButton.addEventListener('click', requestLocation);
            }

            // Function to ensure user has a guest session
            async function ensureGuestSession() {
                try {
                    console.log('🔍 Checking for existing authentication...');

                    // Check if user is authenticated
                    const sessionResponse = await fetch('/login.php?path=/api/check-session');
                    const sessionResult = await sessionResponse.json();

                    if (sessionResult.success && sessionResult.authenticated) {
                        console.log('✅ User is authenticated, no guest session needed');
                        return;
                    }

                    // Check if user has guest session
                    const skipResponse = await fetch('/login.php?path=/api/check-skip-status');
                    const skipResult = await skipResponse.json();

                    if (skipResult.success && skipResult.skipped) {
                        console.log('✅ User has existing guest session');
                        return;
                    }

                    // Create new guest session for new user
                    console.log('🆕 Creating guest session for new user...');
                    const createResponse = await fetch('/login.php?path=/api/skip-authentication', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        }
                    });

                    const createResult = await createResponse.json();

                    if (createResult.success) {
                        console.log('✅ Guest session created successfully');
                    } else {
                        console.error('❌ Failed to create guest session:', createResult.message);
                    }

                } catch (error) {
                    console.error('🚨 Error ensuring guest session:', error);
                }
            }

            // Function to complete guest setup
            async function completeGuestSetup(locationGranted = false) {
                try {
                    console.log('🎯 Completing guest setup with location:', locationGranted);

                    const response = await fetch('/login.php?path=/api/update-guest-setup', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            location_granted: locationGranted,
                            setup_completed: true
                        })
                    });

                    const result = await response.json();

                    if (result.success) {
                        console.log('✅ Guest setup completed successfully');
                    } else {
                        console.error('❌ Guest setup update failed:', result.message);
                    }
                } catch (error) {
                    console.error('🚨 Error completing guest setup:', error);
                }
            }

            // Prevent elastic scroll on iOS
            function preventElasticScroll() {
                let startY = 0;

                document.addEventListener('touchstart', function(e) {
                    startY = e.touches[0].pageY;
                }, { passive: false });

                document.addEventListener('touchmove', function(e) {
                    const y = e.touches[0].pageY;
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    const scrollHeight = document.documentElement.scrollHeight;
                    const clientHeight = window.innerHeight;

                    // Prevent scrolling past the top
                    if (scrollTop <= 0 && y > startY) {
                        e.preventDefault();
                    }

                    // Prevent scrolling past the bottom
                    if (scrollTop + clientHeight >= scrollHeight && y < startY) {
                        e.preventDefault();
                    }
                }, { passive: false });
            }

            // Initialize elastic scroll prevention
            preventElasticScroll();
        });
    </script>
</body>
</html>
