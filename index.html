<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0,viewport-fit=cover">
    <title>MIKO - Ready to Serve!</title>
    <meta name="theme-color" content="#ff5018">
    <script src="https://cdn.tailwindcss.com"></script>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="manifest" crossorigin="use-credentials" href="/manifest.json">

    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="MIKO">
    <link rel="apple-touch-icon" href="/icons/icon-180x180.png">

    <style>
        body {
            font-family: "Inter", sans-serif;
        }

        :root {
            --primary-accent: #ff5018;
            --secondary-accent: #FF7F50;
            --neutral-base: #F7F7F7;
            --text-dark-elements: #333333;
            --subtle-detail: #A7A7A7;
            --pure-white: #FFFFFF;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            height: 100vh;
            width: 100vw;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;
            position: relative;
        }

        .splash-screen-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-image: url("data:image/svg+xml,%3Csvg width='200' height='200' viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='foodPattern' x='0' y='0' width='200' height='200' patternUnits='userSpaceOnUse'%3E%3Cg fill='none' stroke='white' stroke-width='1.5' opacity='0.2'%3E%3C!-- Tomato/Round Item --%3E%3Ccircle cx='40' cy='40' r='18'/%3E%3Cpath d='M40 22 L40 58 M22 40 L58 40'/%3E%3C!-- Chili Pepper 1 --%3E%3Cpath d='M160 30 Q175 15, 185 30 T190 50 T180 70 T160 60 Z'/%3E%3C!-- Cucumber Slice --%3E%3Ccircle cx='140' cy='150' r='15'/%3E%3Cpath d='M140 135 L140 165 M125 150 L155 150'/%3E%3C!-- Leaf --%3E%3Cpath d='M60 110 Q70 100, 80 110 T90 120 T80 130 T70 120 T60 110 Z'/%3E%3C!-- Chili Pepper 2 (rotated) --%3E%3Cg transform='translate(20, 100) rotate(90)'%3E%3Cpath d='M160 30 Q175 15, 185 30 T190 50 T180 70 T160 60 Z'/%3E%3C/g%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect x='0' y='0' width='100%25' height='100%25' fill='url(%23foodPattern)'/%3E%3C/svg%3E");
            background-size: 200px 200px;
            background-repeat: repeat;
            z-index: -1;
            transition: opacity 0.5s ease-out;
        }

        .splash-container {
            width: 100vw;
            height: 100vh;
            max-width: none;
            background-color: var(--primary-accent);
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .logo-container {
            width: 120px;
            height: 120px;
            background: var(--neutral-base);
            border-radius: 30px;
            margin-bottom: 0px; 
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
            animation: pulse 2s infinite;
            z-index: 2;
            opacity: 1;
            pointer-events: auto;
        }

        .logo {
            font-size: 70px;
            color: var(--primary-accent);
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }
    </style>
</head>
<body>
    <div class="splash-screen-overlay"></div>
    <div class="splash-container" id="splashScreen">
        <div class="logo-container" id="logoContainer">
            <i class="fas fa-utensils logo" id="logoIcon"></i>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/service-worker.js')
                    .then(registration => {
                    })
                    .catch(error => {
                    });
            }
            
            const splashScreen = document.getElementById('splashScreen');

            if (!splashScreen) {
                if (!navigator.onLine) {
                    window.location.replace('/offline.html');
                } else {
                    window.location.replace('/home.html');
                }
                return;
            }

            const contentDisplayDuration = 2000000;

            setTimeout(() => {
                const request = indexedDB.open('MikoDB', 1);

                request.onupgradeneeded = function(event) {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains('settings')) {
                        db.createObjectStore('settings', { keyPath: 'id' });
                    }
                };

                request.onsuccess = function(event) {
                    const db = event.target.result;
                    const transaction = db.transaction(['settings'], 'readonly');
                    const objectStore = transaction.objectStore('settings');
                    const getRequest = objectStore.get('isVisitingCustomer');

                    getRequest.onsuccess = function() {
                        const isVisitingCustomer = getRequest.result ? getRequest.result.value : false;

                        if (isVisitingCustomer) {
                            window.location.replace('/home.html');
                        } else {
                            window.location.replace('/setup.html');
                        }
                    };

                    getRequest.onerror = function(event) {
                        window.location.replace('/setup.html');
                    };

                    transaction.onerror = function(event) {
                        window.location.replace('/setup.html');
                    };
                };

                request.onerror = function(event) {
                    if (!navigator.onLine) {
                        window.location.replace('/offline.html');
                    } else {
                        window.location.replace('/setup.html');
                    }
                };
            }, contentDisplayDuration);
        });
    </script>
</body>
</html>
