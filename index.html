<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, maximum-scale=1.0,viewport-fit=cover">
    <title>MIKO - Ready to Serve!</title>
    <meta name="theme-color" content="#ff5018">
    <script src="https://cdn.tailwindcss.com"></script>
    
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0-beta3/css/all.min.css">
    <link rel="manifest" crossorigin="use-credentials" href="/manifest.json">

    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="apple-mobile-web-app-title" content="MIKO">
    <link rel="apple-touch-icon" href="/icons/icon-180x180.png">

    <style>
        body {
            font-family: "Inter", sans-serif;
        }

        :root {
            --primary-accent: #ff5018;
            --secondary-accent: #FF7F50;
            --neutral-base: #F7F7F7;
            --text-dark-elements: #333333;
            --subtle-detail: #A7A7A7;
            --pure-white: #FFFFFF;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;

            /* Prevent touch gestures and zoom */
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        /* iOS PWA optimizations */
        html {
            touch-action: manipulation;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            overscroll-behavior: none;
        }

        body {
            background-color: var(--primary-accent);
            height: 100vh;
            width: 100vw;
            display: flex;
            justify-content: center;
            align-items: center;
            overflow: hidden;

            /* Prevent elastic scroll and touch gestures */
            touch-action: manipulation;
            overscroll-behavior: none;
            -webkit-overflow-scrolling: touch;
            position: fixed;
            top: 0;
            left: 0;
        }

        .splash-container {
            width: 100vw;
            height: 100vh;
            max-width: none;
            background-color: var(--primary-accent);
            box-shadow: 0 15px 50px rgba(0, 0, 0, 0.1);
            position: relative;
            overflow: hidden;
            display: flex;
             background-image: url("data:image/svg+xml,%3Csvg width='200' height='200' viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cdefs%3E%3Cpattern id='foodPattern' x='0' y='0' width='200' height='200' patternUnits='userSpaceOnUse'%3E%3Cg fill='none' stroke='white' stroke-width='1.5' opacity='0.2'%3E%3C!-- Tomato/Round Item --%3E%3Ccircle cx='40' cy='40' r='18'/%3E%3Cpath d='M40 22 L40 58 M22 40 L58 40'/%3E%3C!-- Chili Pepper 1 --%3E%3Cpath d='M160 30 Q175 15, 185 30 T190 50 T180 70 T160 60 Z'/%3E%3C!-- Cucumber Slice --%3E%3Ccircle cx='140' cy='150' r='15'/%3E%3Cpath d='M140 135 L140 165 M125 150 L155 150'/%3E%3C!-- Leaf --%3E%3Cpath d='M60 110 Q70 100, 80 110 T90 120 T80 130 T70 120 T60 110 Z'/%3E%3C!-- Chili Pepper 2 (rotated) --%3E%3Cg transform='translate(20, 100) rotate(90)'%3E%3Cpath d='M160 30 Q175 15, 185 30 T190 50 T180 70 T160 60 Z'/%3E%3C/g%3E%3C/g%3E%3C/pattern%3E%3C/defs%3E%3Crect x='0' y='0' width='100%25' height='100%25' fill='url(%23foodPattern)'/%3E%3C/svg%3E");
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }

        .logo-container {
            width: 120px;
            height: 120px;
            background: var(--neutral-base);
            border-radius: 30px;
            margin-bottom: 0px; 
            display: flex;
            justify-content: center;
            align-items: center;
            box-shadow: 0 10px 30px rgba(255, 107, 53, 0.3);
            animation: pulse 2s infinite;
            z-index: 2;
            opacity: 1;
            pointer-events: auto;
        }

        .logo {
            font-size: 70px;
            color: var(--primary-accent);
        }

        .loading-indicator {
            margin-top: 20px;
            color: white;
            font-size: 14px;
            opacity: 0.8;
            text-align: center;
        }

        .loading-dots {
            display: inline-block;
            animation: dots 1.5s infinite;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        @keyframes dots {
            0%, 20% { content: ''; }
            40% { content: '.'; }
            60% { content: '..'; }
            80%, 100% { content: '...'; }
        }
    </style>
</head>
<body>
    <div class="splash-container" id="splashScreen">
        <div class="logo-container" id="logoContainer">
            <i class="fas fa-utensils logo" id="logoIcon"></i>
        </div>
        <div class="loading-indicator" id="loadingIndicator">
            Preparing your experience<span class="loading-dots"></span>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', () => {
            if ('serviceWorker' in navigator) {
                navigator.serviceWorker.register('/service-worker.js')
                    .then(registration => {
                    })
                    .catch(error => {
                    });
            }

            const splashScreen = document.getElementById('splashScreen');

            if (!splashScreen) {
                if (!navigator.onLine) {
                    window.location.replace('/offline.html');
                } else {
                    window.location.replace('/home.html');
                }
                return;
            }

            const contentDisplayDuration = 3000; // Increased to 3 seconds for preloading

            // PRELOADING SYSTEM - Load all data during splash screen
            const preloadData = async () => {
                console.log('🚀 Starting data preloading...');
                const startTime = performance.now();
                const loadingIndicator = document.getElementById('loadingIndicator');

                const updateLoadingText = (text) => {
                    if (loadingIndicator) {
                        loadingIndicator.innerHTML = `${text}<span class="loading-dots"></span>`;
                    }
                };

                try {
                    updateLoadingText('Loading app data');
                    // Create cache storage for preloaded data
                    const cache = {
                        timestamp: Date.now(),
                        data: {},
                        images: new Set()
                    };

                    // Use comprehensive getAllData endpoint to load everything at once
                    console.log('📡 Fetching ALL data from comprehensive endpoint...');

                    try {
                        const response = await fetch('/home.php?action=getAllData');
                        if (response.ok) {
                            const allData = await response.json();

                            if (allData.error) {
                                throw new Error(allData.error);
                            }

                            // Store all the comprehensive data
                            cache.data = allData;

                            console.log('🎉 Successfully loaded ALL data:');
                            console.log(`📊 Categories: ${allData.metadata?.totalCategories || 0}`);
                            console.log(`🍕 Food Items: ${allData.metadata?.totalFoodItems || 0}`);
                            console.log(`🎁 Offers: ${allData.metadata?.totalOffers || 0}`);
                            console.log(`🏪 Restaurants: ${allData.metadata?.totalRestaurants || 0}`);
                            console.log(`💾 Total Size: ${Math.round((allData.metadata?.dataSize || 0) / 1024)}KB`);

                            // Also store individual endpoints for backward compatibility
                            cache.data.currency = { success: true, currency: allData.currency };
                            cache.data.categories = allData.categories;
                            cache.data.popularFoodItems = allData.popularFoodItems;
                            cache.data.offers = allData.offers;
                            cache.data.restaurants = allData.restaurants;

                            const successfulApis = 1; // Single comprehensive call
                        } else {
                            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                        }
                    } catch (error) {
                        console.error('❌ Failed to load comprehensive data:', error.message);
                        console.log('🔄 Falling back to individual endpoints...');

                        // Fallback to individual endpoints
                        const endpoints = [
                            { key: 'currency', url: '/home.php?action=getCurrency' },
                            { key: 'categories', url: '/home.php?action=getCategories' },
                            { key: 'popularFoodItems', url: '/home.php?action=getFoodItems&category=Popular' },
                            { key: 'offers', url: '/home.php?action=getOffers' },
                            { key: 'restaurants', url: '/home.php?action=getRestaurants' }
                        ];

                        const apiPromises = endpoints.map(async (endpoint) => {
                            try {
                                const response = await fetch(endpoint.url);
                                if (response.ok) {
                                    const data = await response.json();
                                    cache.data[endpoint.key] = data;
                                    console.log(`✓ Fallback loaded ${endpoint.key}`);
                                    return { success: true };
                                }
                                return { success: false };
                            } catch (error) {
                                console.warn(`⚠️ Fallback failed for ${endpoint.key}:`, error.message);
                                return { success: false };
                            }
                        });

                        await Promise.allSettled(apiPromises);
                    }

                    console.log(`📊 Data preloading completed successfully`);
                    updateLoadingText('Loading images');

                    // Preload critical images from ALL data
                    const imagePromises = [];

                    // Preload ALL food item images
                    if (cache.data.foodItems && Array.isArray(cache.data.foodItems)) {
                        cache.data.foodItems.forEach(item => {
                            if (item.image && item.image !== 'null') {
                                imagePromises.push(preloadImage(item.image));
                                cache.images.add(item.image);
                            }
                        });
                    }

                    // Also handle popular items separately for backward compatibility
                    if (cache.data.popularFoodItems && Array.isArray(cache.data.popularFoodItems)) {
                        cache.data.popularFoodItems.forEach(item => {
                            if (item.image && item.image !== 'null') {
                                imagePromises.push(preloadImage(item.image));
                                cache.images.add(item.image);
                            }
                        });
                    }

                    // Preload ALL restaurant images
                    if (cache.data.restaurants && Array.isArray(cache.data.restaurants)) {
                        cache.data.restaurants.forEach(restaurant => {
                            if (restaurant.image && restaurant.image !== 'null') {
                                imagePromises.push(preloadImage(restaurant.image));
                                cache.images.add(restaurant.image);
                            }
                        });
                    }

                    // Preload ALL offer banner images
                    if (cache.data.offers && Array.isArray(cache.data.offers)) {
                        cache.data.offers.forEach(offer => {
                            if (offer.banner && offer.banner !== 'null') {
                                imagePromises.push(preloadImage(offer.banner));
                                cache.images.add(offer.banner);
                            }
                        });
                    }

                    // Wait for critical images to load (with timeout)
                    const imageResults = await Promise.allSettled(imagePromises);
                    const successfulImages = imageResults.filter(r => r.status === 'fulfilled').length;

                    console.log(`🖼️ Image preloading: ${successfulImages}/${imagePromises.length} successful`);
                    updateLoadingText('Finalizing');

                    // Store cache in secure cookies
                    try {
                        const response = await fetch('/home.php?action=setCacheData', {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                            },
                            body: JSON.stringify({ data: cache })
                        });

                        const result = await response.json();
                        if (result.success) {
                            console.log('💾 Cache stored securely in cookies');
                        } else {
                            console.warn('⚠️ Failed to store cache in cookies');
                        }
                    } catch (error) {
                        console.warn('⚠️ Failed to store cache:', error.message);
                    }

                    const endTime = performance.now();
                    const totalTime = Math.round(endTime - startTime);
                    console.log(`🎉 Comprehensive preloading completed in ${totalTime}ms`);
                    console.log(`🖼️ Total images preloaded: ${successfulImages}/${imagePromises.length}`);
                    updateLoadingText('Ready!');

                    return { success: true, totalTime, apiCount: 1, imageCount: successfulImages, totalDataSize: cache.data.metadata?.dataSize || 0 };

                } catch (error) {
                    console.error('❌ Preloading failed:', error);
                    return { success: false, error: error.message };
                }
            };

            // Helper function to preload images
            const preloadImage = (src) => {
                return new Promise((resolve, reject) => {
                    const img = new Image();
                    const timeout = setTimeout(() => {
                        reject(new Error('Image load timeout'));
                    }, 5000); // 5 second timeout per image

                    img.onload = () => {
                        clearTimeout(timeout);
                        resolve(src);
                    };
                    img.onerror = () => {
                        clearTimeout(timeout);
                        reject(new Error(`Failed to load image: ${src}`));
                    };
                    img.src = src;
                });
            };

            // Function to check user status and redirect accordingly
            const checkUserStatusAndRedirect = async () => {
                try {
                    console.log('🔍 Checking user status for routing...');

                    const response = await fetch('/login.php?path=/api/check-user-status');
                    const result = await response.json();

                    if (result.success) {
                        console.log('✅ User status:', result);

                        // Update loading text based on user type
                        const loadingIndicator = document.getElementById('loadingIndicator');
                        if (loadingIndicator) {
                            let statusText = '';
                            switch (result.user_type) {
                                case 'authenticated':
                                    statusText = 'Welcome back!';
                                    break;
                                case 'guest':
                                    statusText = result.is_new_user ? 'Setting up your experience' : 'Welcome back, guest!';
                                    break;
                                case 'new':
                                default:
                                    statusText = 'Welcome to MIKO!';
                                    break;
                            }
                            loadingIndicator.innerHTML = `${statusText}<span class="loading-dots"></span>`;
                        }

                        // Small delay for better UX
                        await new Promise(resolve => setTimeout(resolve, 1000));

                        // Redirect based on user status
                        if (result.redirect_to) {
                            console.log(`🎯 Redirecting to: ${result.redirect_to}`);
                            window.location.replace(`/${result.redirect_to}`);
                        } else {
                            // Fallback to home
                            console.log('🏠 No specific redirect, going to home');
                            window.location.replace('/home.html');
                        }
                    } else {
                        console.error('❌ User status check failed:', result.message);
                        // Fallback to login page
                        window.location.replace('/login.html');
                    }
                } catch (error) {
                    console.error('🚨 Error checking user status:', error);
                    // Fallback to login page
                    window.location.replace('/login.html');
                }
            };

            // Start preloading immediately
            preloadData();

            // Modified navigation logic to wait for preloading
            setTimeout(async () => {
                // Wait for preloading to complete (if still running)
                console.log('⏱️ Checking preloading status...');

                const request = indexedDB.open('MikoDB', 1);

                request.onupgradeneeded = function(event) {
                    const db = event.target.result;
                    if (!db.objectStoreNames.contains('settings')) {
                        db.createObjectStore('settings', { keyPath: 'id' });
                    }
                };

                request.onsuccess = function(event) {
                    const db = event.target.result;
                    const transaction = db.transaction(['settings'], 'readonly');
                    const objectStore = transaction.objectStore('settings');
                    const getRequest = objectStore.get('isVisitingCustomer');

                    getRequest.onsuccess = function() {
                        const isVisitingCustomer = getRequest.result ? getRequest.result.value : false;

                        // Add cache ready flag for home.html to detect
                        localStorage.setItem('mikoDataPreloaded', 'true');

                        console.log('🎯 Navigation ready, cache prepared');

                        // Check user status and redirect accordingly
                        checkUserStatusAndRedirect();
                    };

                    getRequest.onerror = function(event) {
                        localStorage.setItem('mikoDataPreloaded', 'true');
                        checkUserStatusAndRedirect();
                    };

                    transaction.onerror = function(event) {
                        localStorage.setItem('mikoDataPreloaded', 'true');
                        checkUserStatusAndRedirect();
                    };
                };

                request.onerror = function(event) {
                    localStorage.setItem('mikoDataPreloaded', 'true');
                    if (!navigator.onLine) {
                        window.location.replace('/offline.html');
                    } else {
                        checkUserStatusAndRedirect();
                    }
                };
            }, contentDisplayDuration);

            // Prevent elastic scroll on iOS
            function preventElasticScroll() {
                let startY = 0;

                document.addEventListener('touchstart', function(e) {
                    startY = e.touches[0].pageY;
                }, { passive: false });

                document.addEventListener('touchmove', function(e) {
                    const y = e.touches[0].pageY;
                    const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                    const scrollHeight = document.documentElement.scrollHeight;
                    const clientHeight = window.innerHeight;

                    // Prevent scrolling past the top
                    if (scrollTop <= 0 && y > startY) {
                        e.preventDefault();
                    }

                    // Prevent scrolling past the bottom
                    if (scrollTop + clientHeight >= scrollHeight && y < startY) {
                        e.preventDefault();
                    }
                }, { passive: false });
            }

            // Initialize elastic scroll prevention
            preventElasticScroll();
        });
    </script>
</body>
</html>
