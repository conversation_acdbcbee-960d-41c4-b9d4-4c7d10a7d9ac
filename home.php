<?php

// Configure PHP session cookies to be HttpOnly and Secure
ini_set('session.cookie_httponly', 1);
ini_set('session.cookie_secure', isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 1 : 0);
ini_set('session.cookie_samesite', 'Strict');
ini_set('session.use_only_cookies', 1);

// Include secure cookie manager
require_once 'cookie-manager.php';

// Set the content type to JSON
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

// Optimized caching headers
$action = $_GET['action'] ?? $_POST['action'] ?? '';
if (in_array($action, ['getCategories', 'getCurrency'])) {
    header('Cache-Control: public, max-age=3600');
} elseif (in_array($action, ['getFoodItems', 'getOffers', 'getRestaurants'])) {
    header('Cache-Control: public, max-age=300');
}

// Handle preflight requests
if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    http_response_code(200);
    exit();
}

// Supabase configuration
$supabaseUrl = "https://xswrokjllrkdyepluztn.supabase.co";
$supabaseServiceRoleKey = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inhzd3Jva2psbHJrZHllcGx1enRuIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MjczMDU4MywiZXhwIjoyMDY4MzA2NTgzfQ.5Ek5Rlj3Iozc476u9ebVTlREp6RtTRYqmK8Sq_1uzrA";

// Function to make Supabase API requests
function supabaseRequest($endpoint, $method = 'GET', $data = null, $select = null) {
    global $supabaseUrl, $supabaseServiceRoleKey;

    $url = $supabaseUrl . "/rest/v1/" . $endpoint;

    // Add select parameter if provided
    if ($select) {
        $url .= "?select=" . urlencode($select);
    }

    $headers = [
        "apikey: $supabaseServiceRoleKey",
        "Authorization: Bearer $supabaseServiceRoleKey",
        "Content-Type: application/json",
        "Accept: application/json",
        "Prefer: return=minimal" // Reduce response size
    ];

    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10); // Reduced from 30 to 10 seconds
    curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 5); // Connection timeout
    curl_setopt($ch, CURLOPT_HTTP_VERSION, CURL_HTTP_VERSION_2_0); // Use HTTP/2
    curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false); // Skip SSL verification for speed
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false); // Don't follow redirects

    switch ($method) {
        case 'POST':
            curl_setopt($ch, CURLOPT_POST, true);
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
            break;
        case 'PATCH':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'PATCH');
            if ($data) {
                curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
            }
            break;
        case 'DELETE':
            curl_setopt($ch, CURLOPT_CUSTOMREQUEST, 'DELETE');
            break;
    }

    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $curlError = curl_error($ch);
    curl_close($ch);

    if ($curlError) {
        return ['error' => 'Database connection failed'];
    }

    if ($httpCode >= 400) {
        return ['error' => 'Database operation failed'];
    }

    return json_decode($response, true);
}

// Determine the action based on GET or POST request
$action = $_GET['action'] ?? $_POST['action'] ?? '';

switch ($action) {
    case 'setCacheData':
        // Store data in secure cookies
        $input = json_decode(file_get_contents('php://input'), true);
        if ($input && isset($input['data'])) {
            $success = SecureCookieManager::setCacheData($input['data']);
            echo json_encode(['success' => $success]);
        } else {
            echo json_encode(['success' => false, 'error' => 'Invalid data']);
        }
        break;

    case 'getCacheData':
        // Retrieve data from secure cookies
        $cache_data = SecureCookieManager::getCacheData(60); // 1 hour max age
        if ($cache_data) {
            echo json_encode(['success' => true, 'data' => $cache_data]);
        } else {
            echo json_encode(['success' => false, 'data' => null]);
        }
        break;

    case 'clearCache':
        // Clear all cache cookies
        SecureCookieManager::clearAllCookies();
        echo json_encode(['success' => true]);
        break;

    case 'getCategories':
        $result = supabaseRequest('categories', 'GET', null, 'name,emoji,ranking');

        if (isset($result['error'])) {
            echo json_encode(['error' => 'Failed to fetch categories.']);
        } else {
            // Sort by ranking
            usort($result, function($a, $b) {
                return ($a['ranking'] ?? 0) - ($b['ranking'] ?? 0);
            });
            echo json_encode($result);
        }
        break;

    case 'getFoodItems':
        $category = $_GET['category'] ?? 'Popular';
        $search = $_GET['search'] ?? '';

        // First, let's check what columns actually exist
        if (isset($_GET['debug'])) {
            $testEndpoint = "food_items?select=*&limit=1";
            $testResult = supabaseRequest($testEndpoint);
            echo json_encode(['debug' => true, 'endpoint' => $testEndpoint, 'result' => $testResult]);
            break;
        }

        // Select fields with JOIN to restaurants table
        $selectFields = 'id,item_name,description,price,original_price,image,category,top,available,preparation_time,keywords,restaurants(name)';

        if (!empty($search)) {
            // Simple search by item name - use text search instead of ilike
            $searchTerm = urlencode($search);
            $endpoint = "food_items?or=(item_name.ilike.*{$searchTerm}*,keywords.ilike.*{$searchTerm}*)&select={$selectFields}&limit=20&order=id";
        } elseif ($category === 'Popular') {
            // Get popular items (top = 1)
            $endpoint = "food_items?top=eq.1&select={$selectFields}&limit=20&order=id";
        } else {
            // Get items by category - exact match for speed
            $endpoint = "food_items?category=eq." . urlencode($category) . "&select={$selectFields}&limit=50&order=id";
        }

        $result = supabaseRequest($endpoint);

        if (isset($result['error'])) {
            echo json_encode(['error' => 'Failed to fetch food items.']);
        } else {
            // Flatten the restaurant data for easier frontend consumption
            $flattenedResult = [];
            foreach ($result as $item) {
                $flattenedItem = $item;
                $flattenedItem['restaurant_name'] = $item['restaurants']['name'] ?? 'Unknown Restaurant';
                unset($flattenedItem['restaurants']); // Remove the nested object
                $flattenedResult[] = $flattenedItem;
            }
            echo json_encode($flattenedResult);
        }
        break;

    case 'getOffers':
        $endpoint = 'offers?order=expiry_date.asc&select=id,banner,link,discount,flat,restaurant_name,code,expiry_date,created_at,description'; // Get all offers with all fields
        $result = supabaseRequest($endpoint);

        if (isset($result['error'])) {
            echo json_encode(['error' => 'Failed to fetch offers.']);
        } else {
            echo json_encode($result);
        }
        break;

    case 'getRestaurants':
        // Fetch ALL restaurants with complete profile data
        $endpoint = 'restaurants?select=id,name,image,description,restaurant_profile(*)&order=id'; // Get all restaurants with all profile fields
        $result = supabaseRequest($endpoint);

        if (isset($result['error'])) {
            echo json_encode(['error' => 'Failed to fetch restaurants.']);
        } else {
            // Flatten the data structure for easier frontend consumption
            $flattenedResult = [];
            foreach ($result as $restaurant) {
                $profile = $restaurant['restaurant_profile'][0] ?? [];
                $flattenedResult[] = [
                    'id' => $restaurant['id'],
                    'name' => $restaurant['name'],
                    'image' => $restaurant['image'],
                    'description' => $restaurant['description'],
                    'latitude' => $profile['latitude'] ?? null,
                    'longitude' => $profile['longitude'] ?? null,
                    'average_rating' => $profile['average_rating'] ?? 4.0,
                    'review_count' => $profile['review_count'] ?? 0,
                    'delivery_fee' => $profile['delivery_fee'] ?? 0,
                    'cuisine_type' => $profile['cuisine_type'] ?? 'Various',
                    'status' => $profile['status'] ?? 'active',
                    'address' => $profile['address'] ?? null,
                    'city' => $profile['city'] ?? null,
                    'phone' => $profile['phone'] ?? null,
                    'email' => $profile['email'] ?? null,
                    'website_url' => $profile['website_url'] ?? null,
                    'opening_hours' => $profile['opening_hours'] ?? null,
                    'min_order_amount' => $profile['min_order_amount'] ?? 0,
                    'payment_methods' => $profile['payment_methods'] ?? null,
                    'delivery_options' => $profile['delivery_options'] ?? null,
                    'logo_url' => $profile['logo_url'] ?? null
                ];
            }
            echo json_encode($flattenedResult);
        }
        break;

    case 'searchFoodItems':
        $search = $_GET['search'] ?? $_POST['search'] ?? '';
        $category = $_GET['category'] ?? $_POST['category'] ?? '';

        if (empty($search)) {
            echo json_encode(['error' => 'Search term is required.']);
            break;
        }

        $selectFields = 'id,item_name,description,price,original_price,image,restaurant_name,category';
        $searchTerm = urlencode($search);

        // Simplified search for better performance
        if (!empty($category) && $category !== 'All') {
            $endpoint = "food_items?item_name=ilike.*{$searchTerm}*&category=eq." . urlencode($category) . "&select={$selectFields}&limit=10";
        } else {
            $endpoint = "food_items?item_name=ilike.*{$searchTerm}*&select={$selectFields}&limit=10";
        }

        $result = supabaseRequest($endpoint);

        if (isset($result['error'])) {
            echo json_encode(['error' => 'Failed to search food items.']);
        } else {
            echo json_encode($result);
        }
        break;

    case 'addFoodItem':
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $item_name = $_POST['item_name'] ?? '';
            $description = $_POST['description'] ?? '';
            $price = filter_var($_POST['price'] ?? null, FILTER_VALIDATE_FLOAT);
            $original_price = filter_var($_POST['original_price'] ?? null, FILTER_VALIDATE_FLOAT);
            $image = $_POST['image'] ?? '';
            $restaurant_name = $_POST['restaurant_name'] ?? '';
            $restaurant_id = filter_var($_POST['restaurant_id'] ?? null, FILTER_VALIDATE_INT);
            $category = $_POST['category'] ?? '';
            $keywords = $_POST['keywords'] ?? ''; // Custom keywords
            $top = isset($_POST['top']) ? filter_var($_POST['top'], FILTER_VALIDATE_BOOLEAN) : false;
            $available = isset($_POST['available']) ? filter_var($_POST['available'], FILTER_VALIDATE_BOOLEAN) : true;
            $preparation_time = filter_var($_POST['preparation_time'] ?? null, FILTER_VALIDATE_INT);
            $ingredients = $_POST['ingredients'] ?? null;
            $allergens = $_POST['allergens'] ?? null;
            $nutritional_info = $_POST['nutritional_info'] ?? null;

            if (empty($item_name) || $price === false) {
                echo json_encode(['success' => false, 'error' => 'Item name and valid price are required.']);
                break;
            }

            $foodItemData = [
                'item_name' => $item_name,
                'description' => $description,
                'price' => $price,
                'original_price' => $original_price,
                'image' => $image,
                'restaurant_name' => $restaurant_name,
                'restaurant_id' => $restaurant_id,
                'category' => $category,
                'keywords' => $keywords, // Will be auto-generated if empty
                'top' => $top,
                'available' => $available,
                'preparation_time' => $preparation_time,
                'ingredients' => $ingredients ? json_decode($ingredients, true) : null,
                'allergens' => $allergens ? json_decode($allergens, true) : null,
                'nutritional_info' => $nutritional_info ? json_decode($nutritional_info, true) : null
            ];

            $result = supabaseRequest('food_items', 'POST', $foodItemData, 'id');

            if (isset($result['error'])) {
                echo json_encode(['success' => false, 'error' => 'Failed to add food item.']);
            } else {
                echo json_encode(['success' => true, 'id' => $result[0]['id']]);
            }
        } else {
            echo json_encode(['success' => false, 'error' => 'Invalid request method.']);
        }
        break;

    case 'getCurrency':
        $endpoint = 'currency?is_active=eq.1&select=symbol&limit=1';
        $result = supabaseRequest($endpoint);

        if (isset($result['error'])) {
            echo json_encode(['success' => false, 'error' => 'Failed to fetch currency symbol.']);
        } else if (!empty($result) && isset($result[0]['symbol'])) {
            echo json_encode(['success' => true, 'currency' => $result[0]['symbol']]);
        } else {
            echo json_encode(['success' => false, 'error' => 'Currency symbol not found.']);
        }
        break;

    case 'addRestaurant':
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $name = $_POST['name'] ?? '';
            $image = $_POST['image'] ?? '';
            $description = $_POST['description'] ?? '';

            // Profile details
            $owner_id = $_POST['owner_id'] ?? null;
            $address = $_POST['address'] ?? null;
            $city = $_POST['city'] ?? null;
            $latitude = filter_var($_POST['latitude'] ?? null, FILTER_VALIDATE_FLOAT);
            $longitude = filter_var($_POST['longitude'] ?? null, FILTER_VALIDATE_FLOAT);
            $phone = $_POST['phone'] ?? null;
            $email = $_POST['email'] ?? null;
            $website_url = $_POST['website_url'] ?? null;
            $opening_hours = $_POST['opening_hours'] ?? null;
            $cuisine_type = $_POST['cuisine_type'] ?? null;
            $average_rating = filter_var($_POST['average_rating'] ?? null, FILTER_VALIDATE_FLOAT);
            $review_count = filter_var($_POST['review_count'] ?? null, FILTER_VALIDATE_INT);
            $status = $_POST['status'] ?? 'active';
            $delivery_options = $_POST['delivery_options'] ?? null;
            $logo_url = $_POST['logo_url'] ?? null;
            $min_order_amount = filter_var($_POST['min_order_amount'] ?? null, FILTER_VALIDATE_FLOAT);
            $delivery_fee = filter_var($_POST['delivery_fee'] ?? null, FILTER_VALIDATE_FLOAT);
            $payment_methods = $_POST['payment_methods'] ?? null;

            if (empty($name)) {
                echo json_encode(['success' => false, 'error' => 'Restaurant name cannot be empty.']);
                break;
            }

            // First, insert the restaurant
            $restaurantData = [
                'name' => $name,
                'image' => $image,
                'description' => $description
            ];

            $restaurantResult = supabaseRequest('restaurants', 'POST', $restaurantData, 'id');

            if (isset($restaurantResult['error'])) {
                error_log("Error adding restaurant: " . json_encode($restaurantResult));
                echo json_encode(['success' => false, 'error' => 'Failed to add restaurant.']);
                break;
            }

            $restaurant_id = $restaurantResult[0]['id'];

            // Then, insert the restaurant profile
            $profileData = [
                'restaurant_id' => $restaurant_id,
                'name' => $name,
                'description' => $description,
                'owner_id' => $owner_id,
                'address' => $address,
                'city' => $city,
                'latitude' => $latitude,
                'longitude' => $longitude,
                'phone' => $phone,
                'email' => $email,
                'website_url' => $website_url,
                'opening_hours' => $opening_hours ? json_decode($opening_hours, true) : null,
                'cuisine_type' => $cuisine_type,
                'average_rating' => $average_rating,
                'review_count' => $review_count,
                'status' => $status,
                'delivery_options' => $delivery_options ? json_decode($delivery_options, true) : null,
                'logo_url' => $logo_url,
                'min_order_amount' => $min_order_amount,
                'delivery_fee' => $delivery_fee,
                'payment_methods' => $payment_methods ? json_decode($payment_methods, true) : null
            ];

            $profileResult = supabaseRequest('restaurant_profile', 'POST', $profileData);

            if (isset($profileResult['error'])) {
                error_log("Error adding restaurant profile: " . json_encode($profileResult));
                echo json_encode(['success' => false, 'error' => 'Failed to add restaurant profile.']);
            } else {
                echo json_encode(['success' => true, 'id' => $restaurant_id]);
            }
        } else {
            echo json_encode(['success' => false, 'error' => 'Invalid request method.']);
        }
        break;

    case 'updateRestaurant':
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $id = $_POST['id'] ?? '';
            $name = $_POST['name'] ?? '';
            $image = $_POST['image'] ?? '';
            $description = $_POST['description'] ?? '';

            if (empty($id) || empty($name)) {
                echo json_encode(['success' => false, 'error' => 'Restaurant ID and name cannot be empty.']);
                break;
            }

            // Update restaurant
            $restaurantData = [
                'name' => $name,
                'image' => $image,
                'description' => $description
            ];

            $restaurantResult = supabaseRequest("restaurants?id=eq.$id", 'PATCH', $restaurantData);

            if (isset($restaurantResult['error'])) {
                error_log("Error updating restaurant: " . json_encode($restaurantResult));
                echo json_encode(['success' => false, 'error' => 'Failed to update restaurant.']);
                break;
            }

            // Update profile data if provided
            $profileData = [];
            if (isset($_POST['owner_id'])) $profileData['owner_id'] = $_POST['owner_id'];
            if (isset($_POST['address'])) $profileData['address'] = $_POST['address'];
            if (isset($_POST['city'])) $profileData['city'] = $_POST['city'];
            if (isset($_POST['latitude'])) $profileData['latitude'] = filter_var($_POST['latitude'], FILTER_VALIDATE_FLOAT);
            if (isset($_POST['longitude'])) $profileData['longitude'] = filter_var($_POST['longitude'], FILTER_VALIDATE_FLOAT);
            if (isset($_POST['phone'])) $profileData['phone'] = $_POST['phone'];
            if (isset($_POST['email'])) $profileData['email'] = $_POST['email'];
            if (isset($_POST['website_url'])) $profileData['website_url'] = $_POST['website_url'];
            if (isset($_POST['opening_hours'])) $profileData['opening_hours'] = json_decode($_POST['opening_hours'], true);
            if (isset($_POST['cuisine_type'])) $profileData['cuisine_type'] = $_POST['cuisine_type'];
            if (isset($_POST['average_rating'])) $profileData['average_rating'] = filter_var($_POST['average_rating'], FILTER_VALIDATE_FLOAT);
            if (isset($_POST['review_count'])) $profileData['review_count'] = filter_var($_POST['review_count'], FILTER_VALIDATE_INT);
            if (isset($_POST['status'])) $profileData['status'] = $_POST['status'];
            if (isset($_POST['delivery_options'])) $profileData['delivery_options'] = json_decode($_POST['delivery_options'], true);
            if (isset($_POST['logo_url'])) $profileData['logo_url'] = $_POST['logo_url'];
            if (isset($_POST['min_order_amount'])) $profileData['min_order_amount'] = filter_var($_POST['min_order_amount'], FILTER_VALIDATE_FLOAT);
            if (isset($_POST['delivery_fee'])) $profileData['delivery_fee'] = filter_var($_POST['delivery_fee'], FILTER_VALIDATE_FLOAT);
            if (isset($_POST['payment_methods'])) $profileData['payment_methods'] = json_decode($_POST['payment_methods'], true);

            if (!empty($profileData)) {
                $profileData['name'] = $name;
                $profileData['description'] = $description;

                $profileResult = supabaseRequest("restaurant_profile?restaurant_id=eq.$id", 'PATCH', $profileData);

                if (isset($profileResult['error'])) {
                    error_log("Error updating restaurant profile: " . json_encode($profileResult));
                    echo json_encode(['success' => false, 'error' => 'Failed to update restaurant profile.']);
                    break;
                }
            }

            echo json_encode(['success' => true]);
        } else {
            echo json_encode(['success' => false, 'error' => 'Invalid request method.']);
        }
        break;

    case 'deleteRestaurant':
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            $id = $_POST['id'] ?? '';

            if (empty($id)) {
                echo json_encode(['success' => false, 'error' => 'Restaurant ID cannot be empty.']);
                break;
            }

            // Delete restaurant profile first (Supabase will handle cascade)
            $profileResult = supabaseRequest("restaurant_profile?restaurant_id=eq.$id", 'DELETE');

            // Then delete restaurant
            $restaurantResult = supabaseRequest("restaurants?id=eq.$id", 'DELETE');

            if (isset($restaurantResult['error'])) {
                error_log("Error deleting restaurant: " . json_encode($restaurantResult));
                echo json_encode(['success' => false, 'error' => 'Failed to delete restaurant.']);
            } else {
                echo json_encode(['success' => true]);
            }
        } else {
            echo json_encode(['success' => false, 'error' => 'Invalid request method.']);
        }
        break;

    case 'checkSkipLogin':
        $skipped = false;
        if (isset($_COOKIE['skipped_login']) && $_COOKIE['skipped_login'] === '1') {
            $skipped = true;
        }
        echo json_encode(['skipped' => $skipped]);
        break;

    case 'testTables':
        // Test endpoint to check what's in each table
        $testResults = [];

        // Test categories
        $categoriesTest = supabaseRequest('categories');
        $testResults['categories'] = [
            'count' => is_array($categoriesTest) ? count($categoriesTest) : 0,
            'error' => isset($categoriesTest['error']) ? $categoriesTest['error'] : null,
            'sample' => is_array($categoriesTest) && !empty($categoriesTest) ? array_slice($categoriesTest, 0, 2) : null
        ];

        // Test food_items
        $foodItemsTest = supabaseRequest('food_items');
        $testResults['food_items'] = [
            'count' => is_array($foodItemsTest) ? count($foodItemsTest) : 0,
            'error' => isset($foodItemsTest['error']) ? $foodItemsTest['error'] : null,
            'sample' => is_array($foodItemsTest) && !empty($foodItemsTest) ? array_slice($foodItemsTest, 0, 2) : null
        ];

        // Test offers
        $offersTest = supabaseRequest('offers');
        $testResults['offers'] = [
            'count' => is_array($offersTest) ? count($offersTest) : 0,
            'error' => isset($offersTest['error']) ? $offersTest['error'] : null,
            'sample' => is_array($offersTest) && !empty($offersTest) ? array_slice($offersTest, 0, 2) : null
        ];

        // Test restaurants
        $restaurantsTest = supabaseRequest('restaurants');
        $testResults['restaurants'] = [
            'count' => is_array($restaurantsTest) ? count($restaurantsTest) : 0,
            'error' => isset($restaurantsTest['error']) ? $restaurantsTest['error'] : null,
            'sample' => is_array($restaurantsTest) && !empty($restaurantsTest) ? array_slice($restaurantsTest, 0, 2) : null
        ];

        // Test currency
        $currencyTest = supabaseRequest('currency');
        $testResults['currency'] = [
            'count' => is_array($currencyTest) ? count($currencyTest) : 0,
            'error' => isset($currencyTest['error']) ? $currencyTest['error'] : null,
            'sample' => is_array($currencyTest) && !empty($currencyTest) ? array_slice($currencyTest, 0, 2) : null
        ];

        echo json_encode($testResults, JSON_PRETTY_PRINT);
        break;

    case 'createSampleData':
        // Create sample data for testing the preloading system
        $results = [];

        try {
            // Create sample categories
            $sampleCategories = [
                ['name' => 'Pizza', 'emoji' => '🍕', 'ranking' => 1, 'description' => 'Delicious pizzas with various toppings'],
                ['name' => 'Burgers', 'emoji' => '🍔', 'ranking' => 2, 'description' => 'Juicy burgers and sandwiches'],
                ['name' => 'Chinese', 'emoji' => '🥡', 'ranking' => 3, 'description' => 'Authentic Chinese cuisine'],
                ['name' => 'Indian', 'emoji' => '🍛', 'ranking' => 4, 'description' => 'Spicy and flavorful Indian dishes'],
                ['name' => 'Desserts', 'emoji' => '🍰', 'ranking' => 5, 'description' => 'Sweet treats and desserts'],
                ['name' => 'Beverages', 'emoji' => '🥤', 'ranking' => 6, 'description' => 'Refreshing drinks and beverages']
            ];

            foreach ($sampleCategories as $category) {
                $result = supabaseRequest('categories', 'POST', $category);
                if (!isset($result['error'])) {
                    $results['categories'][] = "Created: " . $category['name'];
                }
            }

            // Create sample food items
            $sampleFoodItems = [
                [
                    'item_name' => 'Margherita Pizza',
                    'description' => 'Classic pizza with tomato sauce, mozzarella, and basil',
                    'price' => 12.99,
                    'original_price' => 15.99,
                    'image' => 'https://images.unsplash.com/photo-1604382354936-07c5d9983bd3?w=300',
                    'restaurant_name' => 'Pizza Palace',
                    'category' => 'Pizza',
                    'top' => 1,
                    'available' => true,
                    'preparation_time' => 20
                ],
                [
                    'item_name' => 'Chicken Burger',
                    'description' => 'Grilled chicken breast with lettuce, tomato, and mayo',
                    'price' => 8.99,
                    'original_price' => 10.99,
                    'image' => 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd?w=300',
                    'restaurant_name' => 'Burger House',
                    'category' => 'Burgers',
                    'top' => 1,
                    'available' => true,
                    'preparation_time' => 15
                ],
                [
                    'item_name' => 'Chicken Fried Rice',
                    'description' => 'Wok-fried rice with chicken, vegetables, and soy sauce',
                    'price' => 9.99,
                    'original_price' => 11.99,
                    'image' => 'https://images.unsplash.com/photo-1603133872878-684f208fb84b?w=300',
                    'restaurant_name' => 'Golden Dragon',
                    'category' => 'Chinese',
                    'top' => 1,
                    'available' => true,
                    'preparation_time' => 18
                ],
                [
                    'item_name' => 'Butter Chicken',
                    'description' => 'Creamy tomato-based curry with tender chicken pieces',
                    'price' => 13.99,
                    'original_price' => 16.99,
                    'image' => 'https://images.unsplash.com/photo-1565557623262-b51c2513a641?w=300',
                    'restaurant_name' => 'Spice Garden',
                    'category' => 'Indian',
                    'top' => 1,
                    'available' => true,
                    'preparation_time' => 25
                ],
                [
                    'item_name' => 'Chocolate Cake',
                    'description' => 'Rich chocolate cake with chocolate frosting',
                    'price' => 5.99,
                    'original_price' => 7.99,
                    'image' => 'https://images.unsplash.com/photo-1578985545062-69928b1d9587?w=300',
                    'restaurant_name' => 'Sweet Treats',
                    'category' => 'Desserts',
                    'top' => 0,
                    'available' => true,
                    'preparation_time' => 10
                ],
                [
                    'item_name' => 'Fresh Orange Juice',
                    'description' => 'Freshly squeezed orange juice',
                    'price' => 3.99,
                    'original_price' => 4.99,
                    'image' => 'https://images.unsplash.com/photo-1621506289937-a8e4df240d0b?w=300',
                    'restaurant_name' => 'Juice Bar',
                    'category' => 'Beverages',
                    'top' => 0,
                    'available' => true,
                    'preparation_time' => 5
                ]
            ];

            foreach ($sampleFoodItems as $item) {
                $result = supabaseRequest('food_items', 'POST', $item);
                if (!isset($result['error'])) {
                    $results['food_items'][] = "Created: " . $item['item_name'];
                }
            }

            // Create sample offers
            $sampleOffers = [
                [
                    'banner' => 'https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400',
                    'link' => '#',
                    'discount' => 20,
                    'flat' => 0,
                    'restaurant_name' => 'Pizza Palace',
                    'code' => 'PIZZA20',
                    'expiry_date' => date('Y-m-d', strtotime('+30 days')),
                    'description' => '20% off on all pizzas'
                ],
                [
                    'banner' => 'https://images.unsplash.com/photo-1571091718767-18b5b1457add?w=400',
                    'link' => '#',
                    'discount' => 15,
                    'flat' => 0,
                    'restaurant_name' => 'Burger House',
                    'code' => 'BURGER15',
                    'expiry_date' => date('Y-m-d', strtotime('+25 days')),
                    'description' => '15% off on all burgers'
                ],
                [
                    'banner' => 'https://images.unsplash.com/photo-1565299624946-b28f40a0ca4b?w=400',
                    'link' => '#',
                    'discount' => 0,
                    'flat' => 5,
                    'restaurant_name' => 'All Restaurants',
                    'code' => 'FLAT5',
                    'expiry_date' => date('Y-m-d', strtotime('+20 days')),
                    'description' => '$5 flat discount on orders above $25'
                ]
            ];

            foreach ($sampleOffers as $offer) {
                $result = supabaseRequest('offers', 'POST', $offer);
                if (!isset($result['error'])) {
                    $results['offers'][] = "Created: " . $offer['description'];
                }
            }

            // Create currency entry if not exists
            $currencyData = [
                'symbol' => 'Rs.',
                'is_active' => true,
                'name' => 'Pakistani Rupee',
                'code' => 'PKR'
            ];

            $currencyResult = supabaseRequest('currency', 'POST', $currencyData);
            if (!isset($currencyResult['error'])) {
                $results['currency'] = "Created currency entry";
            }

            echo json_encode(['success' => true, 'results' => $results], JSON_PRETTY_PRINT);

        } catch (Exception $e) {
            echo json_encode(['error' => 'Failed to create sample data', 'details' => $e->getMessage()]);
        }
        break;

    case 'getAllData':
        // Comprehensive endpoint to get ALL data for preloading
        $allData = [];

        try {
            // Get all categories
            $categoriesResult = supabaseRequest('categories', 'GET', null, 'name,emoji,ranking');
            if (!isset($categoriesResult['error'])) {
                usort($categoriesResult, function($a, $b) {
                    return ($a['ranking'] ?? 0) - ($b['ranking'] ?? 0);
                });
                $allData['categories'] = $categoriesResult;
            } else {
                $allData['categories'] = [];
                $allData['errors']['categories'] = $categoriesResult['error'];
            }

            // Get ALL food items with restaurant names via JOIN
            $foodItemsResult = supabaseRequest('food_items', 'GET', null, 'id,item_name,description,price,original_price,image,category,top,available,preparation_time,keywords,restaurants(name)');
            if (!isset($foodItemsResult['error'])) {
                // Flatten the restaurant data for easier frontend consumption
                $flattenedFoodItems = [];
                foreach ($foodItemsResult as $item) {
                    $flattenedItem = $item;
                    $flattenedItem['restaurant_name'] = $item['restaurants']['name'] ?? 'Unknown Restaurant';
                    unset($flattenedItem['restaurants']); // Remove the nested object
                    $flattenedFoodItems[] = $flattenedItem;
                }

                $allData['foodItems'] = $flattenedFoodItems;

                // Group by category for easier access
                $allData['foodItemsByCategory'] = [];
                foreach ($flattenedFoodItems as $item) {
                    $category = $item['category'] ?? 'Other';
                    if (!isset($allData['foodItemsByCategory'][$category])) {
                        $allData['foodItemsByCategory'][$category] = [];
                    }
                    $allData['foodItemsByCategory'][$category][] = $item;
                }

                // Get popular items
                $allData['popularFoodItems'] = array_filter($flattenedFoodItems, function($item) {
                    return $item['top'] == 1;
                });
            } else {
                $allData['foodItems'] = [];
                $allData['foodItemsByCategory'] = [];
                $allData['popularFoodItems'] = [];
                $allData['errors']['foodItems'] = $foodItemsResult['error'];
            }

            // Get ALL offers
            $offersResult = supabaseRequest('offers?order=expiry_date.asc', 'GET', null, 'id,banner,link,discount,flat,restaurant_name,code,expiry_date,created_at,description');
            if (!isset($offersResult['error'])) {
                $allData['offers'] = $offersResult;
            } else {
                $allData['offers'] = [];
                $allData['errors']['offers'] = $offersResult['error'];
            }

            // Get ALL restaurants with profiles
            $restaurantsResult = supabaseRequest('restaurants?select=id,name,image,description,restaurant_profile(*)&order=id');
            if (!isset($restaurantsResult['error'])) {
                $flattenedRestaurants = [];
                foreach ($restaurantsResult as $restaurant) {
                    $profile = $restaurant['restaurant_profile'][0] ?? [];
                    $flattenedRestaurants[] = [
                        'id' => $restaurant['id'],
                        'name' => $restaurant['name'],
                        'image' => $restaurant['image'],
                        'description' => $restaurant['description'],
                        'latitude' => $profile['latitude'] ?? null,
                        'longitude' => $profile['longitude'] ?? null,
                        'average_rating' => $profile['average_rating'] ?? 4.0,
                        'review_count' => $profile['review_count'] ?? 0,
                        'delivery_fee' => $profile['delivery_fee'] ?? 0,
                        'cuisine_type' => $profile['cuisine_type'] ?? 'Various',
                        'status' => $profile['status'] ?? 'active',
                        'address' => $profile['address'] ?? null,
                        'city' => $profile['city'] ?? null,
                        'phone' => $profile['phone'] ?? null,
                        'email' => $profile['email'] ?? null,
                        'website_url' => $profile['website_url'] ?? null,
                        'opening_hours' => $profile['opening_hours'] ?? null,
                        'min_order_amount' => $profile['min_order_amount'] ?? 0,
                        'payment_methods' => $profile['payment_methods'] ?? null,
                        'delivery_options' => $profile['delivery_options'] ?? null,
                        'logo_url' => $profile['logo_url'] ?? null
                    ];
                }
                $allData['restaurants'] = $flattenedRestaurants;
            } else {
                $allData['restaurants'] = [];
                $allData['errors']['restaurants'] = $restaurantsResult['error'];
            }

            // Get currency
            $currencyResult = supabaseRequest('currency?is_active=eq.1&select=symbol&limit=1');
            if (!isset($currencyResult['error']) && !empty($currencyResult) && isset($currencyResult[0]['symbol'])) {
                $allData['currency'] = $currencyResult[0]['symbol'];
            } else {
                $allData['currency'] = 'Rs.'; // Default
            }

            // Add metadata
            $allData['metadata'] = [
                'timestamp' => time(),
                'totalCategories' => count($allData['categories'] ?? []),
                'totalFoodItems' => count($allData['foodItems'] ?? []),
                'totalOffers' => count($allData['offers'] ?? []),
                'totalRestaurants' => count($allData['restaurants'] ?? []),
                'dataSize' => strlen(json_encode($allData))
            ];

            echo json_encode($allData);

        } catch (Exception $e) {
            echo json_encode(['error' => 'Failed to fetch all data']);
        }
        break;

    default:
        echo json_encode(['error' => 'Invalid action']);
        break;
}